2025-08-07 10:00:00 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-07 10:00:00 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 19657 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-08-07 10:00:00 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-08-07 10:00:00 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "sealosDev"
2025-08-07 10:00:01 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-07 10:00:01 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-07 10:00:01 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-08-07 10:00:02 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-08-07 10:00:02 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-07 10:00:02 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-07 10:00:02 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-08-07 10:00:02 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-08-07 10:00:02 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-08-07 10:00:03 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2550 ms
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-08-07 10:00:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-08-07 10:00:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-08-07 10:00:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-08-07 10:00:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-08-07 10:00:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-08-07 10:00:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-08-07 10:00:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-08-07 10:00:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-08-07 10:00:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-08-07 10:00:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-08-07 10:00:03 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-08-07 10:00:03 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-08-07 10:00:03 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-08-07 10:00:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-08-07 10:00:03 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-08-07 10:00:04 INFO  [redisson-netty-1-7] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-08-07 10:00:06 INFO  [redisson-netty-1-20] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ThreadPool' completed initialization.
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - ThreadPool extension configuration applied: coreSize=12, maxSize=24, queueCapacity=2147483647, threadNamePrefix=naya-task-pool, rejectedPolicy=CALLER_RUNS
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS WebClient: baseUrl=[http://wms.fulfillmen.com], skipSslVerification=[false]
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS声明式HTTP接口
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [AUTO, MANUAL, DISABLED]
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - 支持的消息类型: [ORDER_BUYER_VIEW_BUYER_MAKE, ORDER_BUYER_VIEW_ORDER_PAY, ORDER_BATCH_PAY, ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS, ORDER_BUYER_VIEW_PART_PART_SENDGOODS, ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS, ORDER_BUYER_VIEW_ORDER_SUCCESS, ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY]
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 🎯 ProductService优化缓存配置完成:
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 双层缓存策略: 本地内存 + Redis远程
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 空值缓存: 已启用，防止缓存穿透
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   └── 同步机制: 已启用，保证多实例一致性
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.autoconfigure.ValidatorAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Validator' completed initialization.
2025-08-07 10:00:06 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized CompositeLocaleResolver with default locale: en_US and supported locales: [en_US, zh_CN, zh_TW]
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized LocaleChangeInterceptor with param name: lang
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.autoconfigure.SpringDocAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ApiDoc' completed initialization.
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.autoconfigure.SaTokenAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken' completed initialization.
2025-08-07 10:00:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.mvc.WebMvcAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web MVC' completed initialization.
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-08-07 10:00:07 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-08-07 10:00:07 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.a.response.GlobalResponseAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Global Response' completed initialization.
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@65ec8b24
2025-08-07 10:00:07 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.j.autoconfigure.JetCacheAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'JetCache' completed initialization.
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:19657, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=2, lastTimeStamp=1754532007382}] - instanceId:[InstanceId{instanceId=**************:19657, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-08-07 10:00:07 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - TaskScheduler extension configuration applied: poolSize=12, threadNamePrefix=scheduling-, rejectedPolicy=CALLER_RUNS
2025-08-07 10:00:08 DEBUG [main] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogFilter - Filter 'logFilter' configured for use
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 58 ms
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-08-07 10:00:08 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Started BootstrapApplication in 8.236 seconds (process running for 9.008)
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始初始化汇率缓存...
2025-08-07 10:00:08 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始定时刷新汇率缓存...
2025-08-07 10:00:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到USD的汇率
2025-08-07 10:00:08 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到USD的汇率
2025-08-07 10:00:08 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=USD
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-08-07 10:00:08 DEBUG [main] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=USD
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-08-07 10:00:08 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Thu, 07 Aug 2025 02:00:08 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=9e570c1216c4a4adc97cfb436a154725bd527d1fad995bdaa177ebe8dbf82a60; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"USD","currencyT_Name":"美元","currencyFD":"1","exchange":"0.1393","result":"0.1393","updateTime":"2025-08-07 09:55:00"},{"currencyF":"USD","currencyF_Name":"美元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"7.1810","result":"7.1810","updateTime":"2025-08-07 09:55:00"}],"error_code":0}

2025-08-07 10:00:08 DEBUG [reactor-http-nio-15] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Thu, 07 Aug 2025 02:00:08 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=acc5445dc14d568913259cf39515c56257dbd9557791b7875554d865e4f3259f; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"USD","currencyT_Name":"美元","currencyFD":"1","exchange":"0.1393","result":"0.1393","updateTime":"2025-08-07 09:55:00"},{"currencyF":"USD","currencyF_Name":"美元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"7.1810","result":"7.1810","updateTime":"2025-08-07 09:55:00"}],"error_code":0}

2025-08-07 10:00:08 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-08-07 10:00:08 DEBUG [reactor-http-nio-15] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-08-07 10:00:08 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> USD
2025-08-07 10:00:08 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> USD = 0.13
2025-08-07 10:00:08 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-USD = 0.13 (原始: 0.13)
2025-08-07 10:00:08 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> USD
2025-08-07 10:00:08 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> USD = 0.13
2025-08-07 10:00:08 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-USD = 0.13 (原始: 0.13)
2025-08-07 10:00:09 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到EUR的汇率
2025-08-07 10:00:09 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到EUR的汇率
2025-08-07 10:00:09 DEBUG [main] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=EUR
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-08-07 10:00:09 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=EUR
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-08-07 10:00:09 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Thu, 07 Aug 2025 02:00:09 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=84fc6e58304bf01d62817b6a207593ccfcdd413fec26c0aa7123abbc026895df; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"EUR","currencyT_Name":"欧元","currencyFD":"1","exchange":"0.1194","result":"0.1194","updateTime":"2025-08-07 09:55:00"},{"currencyF":"EUR","currencyF_Name":"欧元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"8.3722","result":"8.3722","updateTime":"2025-08-07 09:55:00"}],"error_code":0}

2025-08-07 10:00:09 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-08-07 10:00:09 DEBUG [reactor-http-nio-15] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Thu, 07 Aug 2025 02:00:09 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=37d7c2c25b1c7ed24c34fa22f43633db3a0e866668df4520ba38922a5bd4e789; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"EUR","currencyT_Name":"欧元","currencyFD":"1","exchange":"0.1194","result":"0.1194","updateTime":"2025-08-07 09:55:00"},{"currencyF":"EUR","currencyF_Name":"欧元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"8.3722","result":"8.3722","updateTime":"2025-08-07 09:55:00"}],"error_code":0}

2025-08-07 10:00:09 DEBUG [reactor-http-nio-15] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-08-07 10:00:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> EUR
2025-08-07 10:00:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> EUR = 0.11
2025-08-07 10:00:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-EUR = 0.11 (原始: 0.11)
2025-08-07 10:00:09 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> EUR
2025-08-07 10:00:09 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> EUR = 0.11
2025-08-07 10:00:09 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-EUR = 0.11 (原始: 0.11)
2025-08-07 10:00:09 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到JPY的汇率
2025-08-07 10:00:09 DEBUG [main] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=JPY
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-08-07 10:00:09 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到JPY的汇率
2025-08-07 10:00:09 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=JPY
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-08-07 10:00:09 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Thu, 07 Aug 2025 02:00:09 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=f9e187cb1d7295255aa147a8722dde482c88729c62b4fa3cc37a779db6b5afae; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"JPY","currencyT_Name":"日元","currencyFD":"1","exchange":"20.5448","result":"20.5448","updateTime":"2025-08-07 09:55:00"},{"currencyF":"JPY","currencyF_Name":"日元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.04867","result":"0.04867","updateTime":"2025-08-07 09:55:00"}],"error_code":0}

2025-08-07 10:00:09 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-08-07 10:00:09 DEBUG [reactor-http-nio-15] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Thu, 07 Aug 2025 02:00:09 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=7737a066130aceb4d7bea610d9adfdabab647cc7c048135d00da03e0d2c1d861; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"JPY","currencyT_Name":"日元","currencyFD":"1","exchange":"20.5448","result":"20.5448","updateTime":"2025-08-07 09:55:00"},{"currencyF":"JPY","currencyF_Name":"日元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.04867","result":"0.04867","updateTime":"2025-08-07 09:55:00"}],"error_code":0}

2025-08-07 10:00:09 DEBUG [reactor-http-nio-15] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-08-07 10:00:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> JPY
2025-08-07 10:00:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> JPY = 20.54
2025-08-07 10:00:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-JPY = 20.54 (原始: 20.54)
2025-08-07 10:00:09 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> JPY
2025-08-07 10:00:09 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> JPY = 20.54
2025-08-07 10:00:09 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-JPY = 20.54 (原始: 20.54)
2025-08-07 10:00:09 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到KRW的汇率
2025-08-07 10:00:09 DEBUG [main] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=KRW
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-08-07 10:00:09 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到KRW的汇率
2025-08-07 10:00:09 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=KRW
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-08-07 10:00:09 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Thu, 07 Aug 2025 02:00:10 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=b0b86d0b7f5f770b7b7e08c91d63dd7132a4e32dec285daa6ed7badd6e4b5111; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"successed","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"KRW","currencyT_Name":"韩元","currencyFD":"1","exchange":"192.8886","result":"192.8886","updateTime":"2025-08-07 09:58:00"},{"currencyF":"KRW","currencyF_Name":"韩元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.005184","result":"0.005184","updateTime":"2025-08-07 09:58:00"}],"error_code":0}

2025-08-07 10:00:10 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: successed
2025-08-07 10:00:10 DEBUG [reactor-http-nio-15] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Thu, 07 Aug 2025 02:00:10 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=a34dddb86b4f29dfac8dbf228a5444f3f92e4f46bda1fd2d5455913cad9c79c2; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"successed","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"KRW","currencyT_Name":"韩元","currencyFD":"1","exchange":"192.8886","result":"192.8886","updateTime":"2025-08-07 09:58:00"},{"currencyF":"KRW","currencyF_Name":"韩元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.005184","result":"0.005184","updateTime":"2025-08-07 09:58:00"}],"error_code":0}

2025-08-07 10:00:10 DEBUG [reactor-http-nio-15] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: successed
2025-08-07 10:00:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> KRW
2025-08-07 10:00:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> KRW = 192.88
2025-08-07 10:00:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-KRW = 192.88 (原始: 192.88)
2025-08-07 10:00:10 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> KRW
2025-08-07 10:00:10 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> KRW = 192.88
2025-08-07 10:00:10 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-KRW = 192.88 (原始: 192.88)
2025-08-07 10:00:10 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到INR的汇率
2025-08-07 10:00:10 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到INR的汇率
2025-08-07 10:00:10 DEBUG [main] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=INR
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-08-07 10:00:10 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=INR
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-08-07 10:00:10 DEBUG [reactor-http-nio-15] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Thu, 07 Aug 2025 02:00:10 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=403af62c2683a6e957f35bef5b769b420d8dc8d972c171d394d046c63674f4a6; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"INR","currencyT_Name":"印度卢比","currencyFD":"1","exchange":"12.2200","result":"12.2200","updateTime":"2025-08-07 09:55:00"},{"currencyF":"INR","currencyF_Name":"印度卢比","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.08183","result":"0.08183","updateTime":"2025-08-07 09:55:00"}],"error_code":0}

2025-08-07 10:00:10 DEBUG [reactor-http-nio-15] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-08-07 10:00:10 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Thu, 07 Aug 2025 02:00:10 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=fdfc890e24961217d9cfa8491a00231e86eccb8c481e9625765f079025bf677f; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"INR","currencyT_Name":"印度卢比","currencyFD":"1","exchange":"12.2200","result":"12.2200","updateTime":"2025-08-07 09:55:00"},{"currencyF":"INR","currencyF_Name":"印度卢比","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.08183","result":"0.08183","updateTime":"2025-08-07 09:55:00"}],"error_code":0}

2025-08-07 10:00:10 DEBUG [reactor-http-nio-11] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-08-07 10:00:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> INR
2025-08-07 10:00:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> INR = 12.22
2025-08-07 10:00:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-INR = 12.22 (原始: 12.22)
2025-08-07 10:00:10 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> INR
2025-08-07 10:00:10 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> INR = 12.22
2025-08-07 10:00:10 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-INR = 12.22 (原始: 12.22)
2025-08-07 10:00:10 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-08-07 10:00:10 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-08-07 10:00:10 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 汇率缓存初始化完成，缓存条目数: 5
2025-08-07 10:00:10 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-08-07 10:00:10 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Fulfillmen Shop service started successfully.
2025-08-07 10:00:10 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API地址：http://127.0.0.1:8080
2025-08-07 10:00:10 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API文档：http://127.0.0.1:8080/doc.html
2025-08-07 10:00:10 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-08-07 10:00:10 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-08-07 10:00:10 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-08-07 10:00:10 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 定时刷新汇率缓存完成，缓存条目数: 5
2025-08-07 10:00:10 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-07 10:00:10 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-07 10:00:10 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-07 10:00:10 INFO  [RMI TCP Connection(2)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-07 10:00:11 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@768da48f
2025-08-07 10:00:11 INFO  [RMI TCP Connection(1)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1105434055892328448]:[0] 从自定义域名(localhost)解析到租户ID: 10000
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1105434055892328448]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1105434055892328448]:[0] 租户缓存命中: 10000
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1105434055892328448]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1105434055892328448]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1105434055892328448]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /alibaba/callback)
2025-08-07 10:00:17 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1105434055892328448]:[0] [POST] /alibaba/callback
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1105434055892328448]:[0] 方法 WebhookApi.callBack 有 @RateLimitIgnore 注解，忽略限流
2025-08-07 10:00:17 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.exception.handler.GlobalExceptionHandler - [1105434055892328448]:[0] 未预期异常 - Method: POST, Path: /alibaba/callback
org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter '_aop_signature' for method parameter type String is not present
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValueInternal(RequestParamMethodArgumentResolver.java:220)
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValue(RequestParamMethodArgumentResolver.java:196)
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:125)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:224)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:178)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.shop.common.resolver.CompositeLocaleResolver - [1105434055892328448]:[0] Using default locale: en_US
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] com.fulfillmen.shop.common.util.I18nMessageUtils - [1105434055892328448]:[0] LocaleContextHolder.getLocale() returned: en_US, system default: en_US
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] com.fulfillmen.shop.common.util.I18nMessageUtils - [1105434055892328448]:[0] Using DEFAULT_LOCALE: en_US
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.shop.common.exception.BusinessExceptionI18n - [1105434055892328448]:[0] 创建国际化异常: errorCode=null, i18nKey=global.internal.system.error, locale=en_US, args=[Required request parameter '_aop_signature' for method parameter type String is not present]
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.exception.handler.GlobalExceptionHandler - [1105434055892328448]:[0] Successfully resolved i18n message: key=global.internal.system.error, locale=en_US, message=Internal system error, please contact administrator
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] com.fulfillmen.shop.common.util.I18nMessageUtils - [1105434055892328448]:[0] Cleared current locale context
2025-08-07 10:00:17 WARN  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - [1105434055892328448]:[0] Failure in @ExceptionHandler com.fulfillmen.shop.common.exception.handler.GlobalExceptionHandler#handleGenericException(Exception, HttpServletRequest)
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.toString()" because the return value of "com.fulfillmen.shop.common.exception.BusinessExceptionI18n.getErrorCode()" is null
	at com.fulfillmen.shop.common.exception.handler.GlobalExceptionHandler.handleGenericException(GlobalExceptionHandler.java:653)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:432)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:74)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:175)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1358)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1161)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1106)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-07 10:00:17 WARN  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] o.s.w.s.m.support.DefaultHandlerExceptionResolver - [1105434055892328448]:[0] Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter '_aop_signature' for method parameter type String is not present]
2025-08-07 10:00:17 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1105434055892328448]:[0] [POST] /alibaba/callback 400 16ms
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1105434055892328448]:[0] 清理增强租户上下文
2025-08-07 10:00:17 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1105434055892328448]:[0] 过滤器清理租户上下文完成
2025-08-07 10:00:17 INFO  [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [POST] /error
2025-08-07 10:00:17 INFO  [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [POST] /error 400 17ms
