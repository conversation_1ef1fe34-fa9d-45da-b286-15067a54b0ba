/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/7/16 10:59
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum OrderStatusEnums {

    /**
     * 等待买家付款
     */
    WAIT_BUYER_PAY("waitbuyerpay", "等待买家付款"),
    /**
     * 等待卖家发货
     */
    WAIT_SELLER_SEND("waitsellersend", "等待卖家发货"),
    /**
     * 等待买家收货
     */
    WAIT_BUYER_RECEIVE("waitbuyerreceive", "等待买家收货"),
    /**
     * 已收货
     */
    CONFIRM_GOODS("confirm_goods", "已收货"),
    /**
     * 已收货
     */
    CONFIRM_GOODS_AND_HAS_SUBSIDY("confirm_goods_and_has_subsidy", "已收货"),
    /**
     * 交易成功
     */
    SUCCESS("success", "交易成功"),
    /**
     * 交易取消
     */
    CANCEL("cancel", "交易取消"),
    /**
     * 交易取消
     */
    TERMINATED("terminated", "交易终止");

    @JsonValue
    private final String code;
    private final String desc;

    OrderStatusEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static OrderStatusEnums fromCode(String code) {
        return Arrays.stream(OrderStatusEnums.values())
            .filter(status -> Objects.equals(status.getCode(), code))
            .findFirst()
            .orElse(null);
    }
}
