/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.TzProductSkuDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemLogisticsStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseBuyerTypeEnums;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import com.fulfillmen.shop.manager.core.order.helper.OrderContextHelper;
import com.fulfillmen.shop.manager.core.repository.TzOrderPurchaseRepository;
import com.fulfillmen.shop.manager.service.IProductSyncService;
import com.fulfillmen.shop.manager.support.alibaba.webhook.convert.AlibabaOrderConvert;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderDataSyncService;
import com.fulfillmen.starter.core.exception.BusinessException;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo.NewStepOrder;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeNativeLogisticsInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeProductItem;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeSellContact;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse.OrderDetail;
import com.fulfillmen.support.alibaba.enums.OrderStatusEnums;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

/**
 * 订单数据同步服务实现
 *
 * <pre>
 * 实现要点：
 * 1. 数据存在性检查和完整性验证
 * 2. 缺失数据的智能补齐
 * 3. 新旧版本数据的兼容处理
 * 4. 数据关联关系的正确建立
 * 5. 状态和时间戳的合理初始化
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/25 16:40
 * @description 订单数据同步服务实现
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderDataSyncServiceImpl implements OrderDataSyncService {

    private static final Long TENANT_ID = 10000L;
    private final TzOrderPurchaseRepository orderPurchaseRepository;
    private final TzOrderSupplierMapper orderSupplierMapper;
    private final TzOrderItemMapper orderItemMapper;
    private final IProductSyncService productSyncService;
    private final TransactionTemplate transactionTemplate;
    private final AlibabaOrderConvert alibabaOrderConvert;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public OrderDataIntegrityResult checkOrderDataIntegrity(OrderDetail orderDetail) {
        log.debug("开始检查订单数据完整性: orderDetail={}", JacksonUtil.toJsonString(orderDetail));
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();
        // 1. 检查当前的供应商订单
        TzOrderSupplier existingSupplierOrders = findSupplierOrderByExternalOrderId(baseInfo.getIdOfStr());
        if (Objects.isNull(existingSupplierOrders)) {
            log.info("未找到供应商订单: orderDetail={}", orderDetail);
            return new OrderDataIntegrityResult(false, false, false, false, true, null, Collections.emptyList(),
              Collections.emptyList());
        }

        // 2. 异步并行查询相关数据
        CompletableFuture<TzOrderPurchase> purchaseOrderFuture = findPurchaseOrderByPurchaseOrderIdAsync(
          existingSupplierOrders.getPurchaseOrderId());
        CompletableFuture<List<TzOrderItem>> orderItemsFuture = findOrderItemsByPurchaseOrderIdAsync(
          existingSupplierOrders.getPurchaseOrderId());
        CompletableFuture<List<TzOrderSupplier>> supplierOrdersFuture = findSupplierOrdersByPurchaseOrderIdAsync(
          existingSupplierOrders.getPurchaseOrderId());

        try {
            // 3. 等待所有异步任务完成并获取结果 ， 等待所有任务完成
            CompletableFuture.allOf(purchaseOrderFuture, supplierOrdersFuture,
              orderItemsFuture).join();

            // 4. 获取查询结果
            TzOrderPurchase purchaseOrder = purchaseOrderFuture.get();
            List<TzOrderSupplier> supplierOrders = supplierOrdersFuture.get();
            List<TzOrderItem> orderItems = orderItemsFuture.get();

            // 5. 检查数据完整性
            boolean hasPurchaseOrder = purchaseOrder != null;
            boolean hasSupplierOrders = !CollectionUtils.isEmpty(supplierOrders);
            boolean hasOrderItems = !CollectionUtils.isEmpty(orderItems);

            // 6. 判断数据完整性
            boolean isDataComplete = hasPurchaseOrder && hasSupplierOrders && hasOrderItems;
            boolean isLegacyData = !isDataComplete;

            OrderDataIntegrityResult result = new OrderDataIntegrityResult(hasPurchaseOrder, hasSupplierOrders,
              hasOrderItems, isDataComplete, isLegacyData, purchaseOrder,
              supplierOrders, orderItems);

            log.info("订单数据完整性检查完成: orderId={}, result={}", orderDetail, result);
            return result;

        } catch (Exception e) {
            log.error("订单数据完整性检查异常: orderId={}", orderDetail, e);
            // 异常情况下，回退到同步方式处理
            // return checkOrderDataIntegritySync(orderId, existingSupplierOrders);
        }
        return new OrderDataIntegrityResult(false, false, false, false, true, null, Collections.emptyList(),
          Collections.emptyList());
    }

    /**
     * 根据外部订单ID查询供应商订单
     *
     * @param purchaseOrderId 采购订单ID
     * @return 供应商订单列表
     */
    private CompletableFuture<List<TzOrderSupplier>> findSupplierOrdersByPurchaseOrderIdAsync(Long purchaseOrderId) {
        return CompletableFuture.supplyAsync(
          () -> this.orderSupplierMapper.listByPurchaseOrderIdAndIgnoreTenantId(purchaseOrderId),
          threadPoolTaskExecutor);
    }

    /**
     * 根据 id 查询采购订单
     *
     * @param purchaseOrderId 采购订单ID
     * @return 采购订单
     */
    private CompletableFuture<TzOrderPurchase> findPurchaseOrderByPurchaseOrderIdAsync(Long purchaseOrderId) {
        return CompletableFuture.supplyAsync(
          () -> this.orderPurchaseRepository.getByIdAndIgnoreTenantId(purchaseOrderId), threadPoolTaskExecutor);
    }

    @Override
    public OrderContextRecord syncAndCompleteOrderData(String orderId, OrderDataIntegrityResult integrityResult,
      OrderDetail orderDetail,
      List<WmsPurchaseOrderDetailsRes> wmsOrderDetails) {
        log.info("开始同步和补齐订单数据: orderId={}, needsSync={}", orderId, integrityResult.needsDataSync());

        if (integrityResult.isDataComplete()) {
            // 数据完整，直接返回现有数据
            log.debug("订单数据完整，直接使用现有数据: orderId={}", orderId);
            return buildOrderContextRecord(integrityResult, orderDetail, wmsOrderDetails);
        }

        // 数据不完整，需要补齐
        return syncMissingOrderData(orderId, integrityResult, orderDetail, wmsOrderDetails);
    }

    /**
     * 同步缺失的订单数据 - 统一事务处理
     *
     * <pre>
     * 正确的事务处理：采购订单、供应商订单、订单项作为一个完整的业务操作，
     * 必须在同一事务中保证数据一致性。任何一步失败都要回滚所有操作。
     * </pre>
     */
    private OrderContextRecord syncMissingOrderData(String orderId, OrderDataIntegrityResult integrityResult,
      OrderDetail orderDetail,
      List<WmsPurchaseOrderDetailsRes> wmsOrderDetails) {
        log.info("开始补齐缺失的订单数据: orderId={}, missing={}", orderId, integrityResult.getMissingDataDescription());
        // 获取WMS订单详情（如果存在）
        WmsPurchaseOrderDetailsRes wmsOrderDetail = CollectionUtils.isEmpty(wmsOrderDetails) ? null
          : wmsOrderDetails.getFirst();
        if (Objects.isNull(wmsOrderDetail)) {
            log.warn("wms 采购订单缺失。无法补齐数据");
            throw new BusinessException("wms purchase order is require");
        }

        try {
            // 开启事务保存
            return transactionTemplate.execute(status -> {
                try {
                    // 使用简单的包装类解决变量作用域问题
                    final OrderDataHolder dataHolder = new OrderDataHolder(integrityResult);

                    // 1. 补齐采购订单
                    if (!integrityResult.hasPurchaseOrder()) {
                        dataHolder.purchaseOrder = createAndSavePurchaseOrder(orderDetail, wmsOrderDetail);
                        log.info("创建采购订单成功: orderId={}, purchaseOrderId={}", orderId, dataHolder.purchaseOrder.getId());
                    }

                    if (dataHolder.purchaseOrder == null) {
                        throw new IllegalStateException("采购订单不能为空");
                    }

                    // 2. 补齐供应商订单
                    if (!integrityResult.hasSupplierOrders()) {
                        dataHolder.supplierOrders = createAndSaveSupplierOrders(dataHolder.purchaseOrder.getId(),
                          orderDetail, wmsOrderDetail);
                        log.info("创建供应商订单成功: orderId={}, supplierOrderCount={}", orderId,
                          dataHolder.supplierOrders.size());
                    }

                    // 3. 补齐订单项
                    if (!integrityResult.hasOrderItems() && !CollectionUtils.isEmpty(dataHolder.supplierOrders)) {
                        Long supplierOrderId = dataHolder.supplierOrders.getFirst().getId();
                        dataHolder.orderItems = createAndSaveOrderItems(dataHolder.purchaseOrder.getId(),
                          supplierOrderId, orderDetail, wmsOrderDetail);
                        log.info("创建订单项成功: orderId={}, orderItemCount={}", orderId, dataHolder.orderItems.size());
                    }

                    // 构建并返回订单上下文记录
                    log.info("订单数据补齐完成: orderId={}, purchaseOrderId={}, supplierOrderCount={}, orderItemCount={}",
                      orderId, dataHolder.purchaseOrder.getId(),
                      dataHolder.supplierOrders.size(), dataHolder.orderItems.size());

                    return OrderContextRecord.builder().alibabaOrderDetail(orderDetail)
                      .wmsPurchaseOrderDetailsRes(wmsOrderDetails).tzOrderPurchase(dataHolder.purchaseOrder)
                      .tzOrderSuppliers(dataHolder.supplierOrders).tzOrderItems(dataHolder.orderItems).build();

                } catch (Exception e) {
                    status.setRollbackOnly();
                    log.error("事务执行过程中发生异常: orderId={}", orderId, e);
                    throw new BusinessException("订单数据补齐事务执行失败", e);
                }
            });
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("订单数据补齐失败: orderId={}", orderId, e);
            throw new BusinessException("订单数据补齐失败", e);
        }
    }

    /**
     * 创建并保存采购订单
     */
    private TzOrderPurchase createAndSavePurchaseOrder(OrderDetail orderDetail,
      WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        TzOrderPurchase purchaseOrder = createPurchaseOrderData(orderDetail, wmsOrderDetail);
        // 保存采购订单到数据库
        boolean success = orderPurchaseRepository.save(purchaseOrder);
        if (!success) {
            throw new RuntimeException("采购订单保存失败");
        }
        return purchaseOrder;
    }

    /**
     * 创建并保存供应商订单
     */
    private List<TzOrderSupplier> createAndSaveSupplierOrders(Long purchaseOrderId, OrderDetail orderDetail,
      WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        List<TzOrderSupplier> supplierOrders = createSupplierOrderData(purchaseOrderId, orderDetail, wmsOrderDetail);
        if (!CollectionUtils.isEmpty(supplierOrders)) {
            orderSupplierMapper.insertBatch(supplierOrders);
        }
        return supplierOrders;
    }

    /**
     * 创建并保存订单项
     */
    private List<TzOrderItem> createAndSaveOrderItems(Long purchaseOrderId, Long supplierOrderId,
      OrderDetail orderDetail, WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        List<TzOrderItem> orderItems = createOrderItemData(purchaseOrderId, supplierOrderId, orderDetail,
          wmsOrderDetail);
        if (!CollectionUtils.isEmpty(orderItems)) {
            orderItemMapper.insertBatch(orderItems);
        }
        return orderItems;
    }

    @Override
    public TzOrderPurchase createPurchaseOrderData(OrderDetail orderDetail, WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();
        TradeNativeLogisticsInfo logistics = orderDetail.getNativeLogistics();
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
        TzOrderPurchase tzOrderPurchase = TzOrderPurchase.builder().id(idGenerator.generate())
          .purchaseOrderNo(OrderContextHelper.generateOrderNo("C"))
          // 旧版数据默认买家ID为0 ，也许不是平台的用户。
          .buyerId(0L).buyerType(TzOrderPurchaseBuyerTypeEnums.WMS)
          .orderStatus(TzOrderPurchaseStatusEnum.PAYMENT_PENDING).orderDate(baseInfo.getCreateTime())
          // ----------------------- 客户支付费用信息 -----------------------
          // 用户支付的产品总价
          .customerGoodsAmount(wmsOrderDetail.getProductSalesTotalAmount())
          // 用户支付运费
          .customerTotalFreight(wmsOrderDetail.getShippingFee())
          // 用户支付服务费
          .serviceFee(wmsOrderDetail.getServiceFee() != null ? wmsOrderDetail.getServiceFee() : BigDecimal.ZERO)
          // 用户支付产品+运费
          .customerTotalAmount(wmsOrderDetail.getTotalAmount())
          // ----------------------- 应付费用信息 -----------------------
          // 总优惠换算成元
          .payableDiscountAmount(baseInfo.getDiscount().multiply(BigDecimal.valueOf(100)))
          // 会员折扣
          // .payablePlusDiscountAmount(BigDecimal.ZERO)
          // 红包/店铺优惠
          .payableCouponAmount(baseInfo.getCouponFee())
          // 商品总成本
          .payableGoodsAmount(baseInfo.getSumProductPayment())
          // 运费
          .payableFreightTotal(baseInfo.getShippingFee())
          // 应付总价
          .payableAmountTotal(baseInfo.getTotalAmount())
          // ----------------------- 实付费用信息 -----------------------
          // 实付总金额
          .actualPaymentAmount(wmsOrderDetail.getAlibabaFinalAmount())
          // 实付总产品
          .actualPaymentGoodsAmount(wmsOrderDetail.getProductFinalTotalAmount())
          // 实付运费
          .actualPaymentFreightAmount(wmsOrderDetail.getFinalShoppingFee())
          // 实付折扣
          .actualPaymentDiscountAmount(wmsOrderDetail.getDiscount())
          // 实付红包/店铺优惠
          .actualPaymentCouponAmount(wmsOrderDetail.getCouponDiscount())
          // 实付Plus会员折扣
          .actualPaymentPlusAmount(wmsOrderDetail.getPlusDiscount())

          // 旧版数据默认汇率为0 ， 为实时汇率方式计算
          .exchangeRateSnapshot(BigDecimal.ZERO)
          // 收货地址信息 2025 年 07 月 28 日 17:42:46 后期废弃
          .deliveryAddress(logistics != null ? logistics.getAddress() : "")
          .postalCode(logistics != null ? logistics.getZip() : "")
          .province(logistics != null ? logistics.getProvince() : "")
          .city(logistics != null ? logistics.getCity() : "")
          .district(logistics != null ? logistics.getArea() : "")
          .consigneeName(logistics != null ? logistics.getContactPerson() : "")
          .consigneePhone(logistics != null ? logistics.getMobile() : "")
          // 统计信息
          // 默认一个供应商
          .supplierCount(1)
          .lineItemCount(orderDetail.getProductItems() != null ? orderDetail.getProductItems().size() : 0)
          .totalQuantity(calculateTotalQuantity(orderDetail.getProductItems())).completedSupplierCount(0)
          .tenantId(TENANT_ID)
          // 系统字段
          .build();

        // 判断该订单是否交易了。
        OrderStatusEnums orderStatusEnums = OrderStatusEnums.fromCode(baseInfo.getStatus());
        // 已付款
        boolean isPaid = baseInfo.getPayTime() != null && !orderStatusEnums.equals(OrderStatusEnums.WAIT_BUYER_PAY);

        if (isPaid) {
            // 旧数据的信息，以 wms 订单信息为主
            // 当前采购订单状态 以 wms 状态为主
            tzOrderPurchase.setOrderStatus(alibabaOrderConvert.convertPurchaseOrderStatus(wmsOrderDetail.getStatus()));
            // 实付总价
            tzOrderPurchase.setActualPaymentAmount(wmsOrderDetail.getTotalAmount());
            // 实付产品总价
            tzOrderPurchase.setActualPaymentGoodsAmount(wmsOrderDetail.getProductFinalTotalAmount());
            // 实付运费
            tzOrderPurchase.setActualPaymentFreightAmount(wmsOrderDetail.getFinalShoppingFee());
            // 实付折扣
            tzOrderPurchase.setActualPaymentDiscountAmount(wmsOrderDetail.getDiscount());
            // 实付红包/店铺优惠
            tzOrderPurchase.setActualPaymentCouponAmount(wmsOrderDetail.getCouponDiscount());
            // 实付Plus会员折扣
            tzOrderPurchase.setActualPaymentPlusAmount(wmsOrderDetail.getPlusDiscount());
        }

        return tzOrderPurchase;
    }

    @Override
    public List<TzOrderSupplier> createSupplierOrderData(Long purchaseOrderId, OrderDetail orderDetail,
      WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        List<TzOrderSupplier> supplierOrders = new ArrayList<>();
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();
        TradeSellContact sellerContact = baseInfo.getSellerContact();
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
        // 为兼容旧版，创建一个默认的供应商订单
        TzOrderSupplier supplierOrder = TzOrderSupplier.builder().id(idGenerator.generate())
          .purchaseOrderId(purchaseOrderId)
          .supplierOrderNo(OrderContextHelper.generateOrderNo("SO"))
          .platformCode(PlatformCodeEnum.PLATFORM_CODE_1688).wmsSyncStatus(OrderSupplierSyncStatusEnums.SYNCED)
          .wmsPurchaseOrderNo(wmsOrderDetail.getPurchaseNo())
          .externalSyncStatus(OrderSupplierSyncStatusEnums.SYNCED).platformOrderId(baseInfo.getIdOfStr())
          .metadataJson(JacksonUtil.toJsonString(orderDetail))
          .supplierId(baseInfo.getSellerId() != null ? baseInfo.getSellerId() : "unknown")
          .supplierName(sellerContact != null ? sellerContact.getCompanyName() : "unknown")
          .supplierShopName(sellerContact != null ? sellerContact.getShopName() : "unknown")
          .platformOrderId(baseInfo.getIdOfStr())
          .status(alibabaOrderConvert.convertSupplierOrderStatus(OrderStatusEnums.fromCode(baseInfo.getStatus())))
          // 客户支付信息
          .customerGoodsAmount(wmsOrderDetail.getProductSalesTotalAmount())
          .customerFreightAmount(wmsOrderDetail.getShippingFee())
          .customerTotalAmount(wmsOrderDetail.getTotalAmount()).serviceFee(wmsOrderDetail.getServiceFee())
          // 1688 应付信息
          .payableGoodsAmount(baseInfo.getSumProductPayment()).payableFreightAmount(baseInfo.getShippingFee())
          .payableAmountTotal(baseInfo.getTotalAmount())
          .payableDiscountAmount(baseInfo.getDiscount().multiply(BigDecimal.valueOf(100)))
          .payableCouponAmount(baseInfo.getCouponFee()).payablePlusDiscountAmount(BigDecimal.ZERO)
          // 实付信息
          .actualPaymentAmount(wmsOrderDetail.getTotalAmount())
          .actualPaymentGoodsAmount(wmsOrderDetail.getProductFinalTotalAmount())
          .actualPaymentFreightAmount(wmsOrderDetail.getFinalShoppingFee())
          .actualPaymentDiscountAmount(wmsOrderDetail.getDiscount())
          .actualPaymentCouponAmount(wmsOrderDetail.getCouponDiscount())
          .actualPaymentPlusAmount(wmsOrderDetail.getPlusDiscount())
          // 1688 订单创建时间
          .orderDate(baseInfo.getCreateTime())
          .lineItemCount(orderDetail.getProductItems() != null ? orderDetail.getProductItems().size() : 0)
          .completedItemCount(0)
          .tenantId(TENANT_ID).build();
        // 全款支付
        // FIXME: 2025/7/30 暂不支持分阶段支付，后期处理。
        NewStepOrder newStepOrder = baseInfo.getNewStepOrderList() != null ? baseInfo.getNewStepOrderList().getFirst()
          : null;
        // 实付信息
        if (newStepOrder != null) {
            supplierOrder.setActualPaymentAmount(newStepOrder.getPaidFee());
            supplierOrder.setActualPaymentGoodsAmount(newStepOrder.getGoodsFee());
            supplierOrder.setActualPaymentFreightAmount(newStepOrder.getPaidPostFee());
            supplierOrder.setActualPaymentDiscountAmount(newStepOrder.getDiscountFee());
            supplierOrder.setActualPaymentCouponAmount(newStepOrder.getAdjustFee());
        }

        supplierOrders.add(supplierOrder);
        return supplierOrders;
    }

    @Override
    public List<TzOrderItem> createOrderItemData(Long purchaseOrderId, Long supplierOrderId, OrderDetail orderDetail,
      WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        List<TzOrderItem> orderItems = new ArrayList<>();
        List<TradeProductItem> productItems = orderDetail.getProductItems();
        if (CollectionUtils.isEmpty(productItems)) {
            return orderItems;
        }
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
        for (int i = 0; i < productItems.size(); i++) {
            TradeProductItem productItem = productItems.get(i);
            Long offerId = productItem.getProductId();
            // 检查是否为单品
            boolean isSingleItem = productSyncService.isSingleItem(String.valueOf(offerId));
            // 获取并同步 ProductId
            TzProductDTO productDTO = this.productSyncService.getOrSyncProductByPlatformId(String.valueOf(offerId));
            Long spuId = productDTO != null ? productDTO.getId() : 0L;
            Long skuId = productDTO != null && productDTO.getIsSingleItem().equals(TzProductSpuSingleItemEnum.YES)
              ? productDTO.getSkuList().getFirst().getId()
              : 0L;
            // 商品的物流信息
            TzOrderItemLogisticsStatusEnum logisticsStatusEnum = TzOrderItemLogisticsStatusEnum
              .getByCode(productItem.getLogisticsStatus());
            Map<Long, TzProductSkuDTO> skuMap = productDTO != null
              ? productDTO.getSkuList().stream().collect(Collectors.toMap(TzProductSkuDTO::getId, sku -> sku))
              : Collections.emptyMap();
            // 获取 sku 信息
            TzProductSkuDTO sku = skuMap.get(skuId);
            List<AttrJson> specs;
            if (Objects.nonNull(sku)) {
                specs = sku.getSpecs();
            } else {
                if (!isSingleItem) {
                    specs = new ArrayList<>();
                    productItem.getSkuInfos().forEach(skuInfo -> {
                        specs.add(AttrJson.builder().attrKey(skuInfo.getName()).attrValue(skuInfo.getValue()).build());
                    });
                } else {
                    specs = null;
                }
            }

            // 创建订单项
            TzOrderItem orderItem = TzOrderItem.builder().id(idGenerator.generate()).purchaseOrderId(purchaseOrderId)
              .supplierOrderId(supplierOrderId).lineNumber(i + 1)
              .productSpuId(spuId)
              // 如果是单品 id
              .productSkuId(skuId)
              .platformProductId(
                productItem.getProductId() != null ? productItem.getProductId().toString() : null)
              .platformSkuId(productItem.getSkuId() != null ? productItem.getSkuId().toString() : null)
              .platformSpecId(productItem.getSpecId() != null ? productItem.getSpecId() : null)
              .platformOrderId(orderDetail.getBaseInfo().getIdOfStr())
              .platformMetadata(JacksonUtil.toJsonString(productItem))
              .platformSnapshotUrl(productItem.getProductSnapshotUrl())
              // 1688 订单详情 ID
              .platformItemId(productItem.getSubItemIdString()).externalItemId(productItem.getSubItemIdString())
              // 商品信息
              .productTitle(Objects.nonNull(productDTO) ? productDTO.getTitle() : productItem.getName())
              .productTitleEn(Objects.nonNull(productDTO) ? productDTO.getTitleTrans() : null)
              .productLink(productItem.getProductSnapshotUrl()).skuSpecs(specs)
              .productImageUrl(productItem.getProductImgUrl() == null
                || CollectionUtils.isEmpty(List.of(productItem.getProductImgUrl())) ? ""
                : productItem.getProductImgUrl()[0])
              .price(productItem.getPrice()).quantity(productItem.getQuantity())
              // 商品小计
              .totalAmount(productItem.getItemAmount()).unit(productItem.getUnit())
              // 这里要修改
              // .status(TzOrderItemStatusEnum.PENDING)
              // 默认未发货
              .logisticsStatus(logisticsStatusEnum)
              // 收货时间
              .completedDatetime(productItem.getGmtCompleted())
              .isSingleItem(isSingleItem ? TzProductSpuSingleItemEnum.YES : TzProductSpuSingleItemEnum.NO)
              .gmtCreated(productItem.getGmtCreate()).gmtModified(productItem.getGmtModified())
              .tenantId(TENANT_ID).build();

            orderItems.add(orderItem);
        }

        return orderItems;
    }

    /**
     * 构建订单上下文记录
     */
    private OrderContextRecord buildOrderContextRecord(OrderDataIntegrityResult integrityResult,
      OrderDetail orderDetail, List<WmsPurchaseOrderDetailsRes> wmsOrderDetails) {
        return OrderContextRecord.builder().alibabaOrderDetail(orderDetail).wmsPurchaseOrderDetailsRes(wmsOrderDetails)
          .tzOrderPurchase(integrityResult.existingPurchaseOrder())
          .tzOrderSuppliers(integrityResult.existingSupplierOrders())
          .tzOrderItems(integrityResult.existingOrderItems()).build();
    }

    /**
     * 根据外部订单ID查找供应商订单 一个 1688 订单只会对应一个 供应商订单
     */
    private TzOrderSupplier findSupplierOrderByExternalOrderId(String orderId) {
        return orderSupplierMapper.findByPlatformOrderIdAndIgnoreTenantId(orderId);
    }

    /**
     * 根据采购订单ID查找订单项
     */
    private CompletableFuture<List<TzOrderItem>> findOrderItemsByPurchaseOrderIdAsync(Long purchaseOrderId) {
        return CompletableFuture.supplyAsync(() -> {
            LambdaQueryWrapper<TzOrderItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TzOrderItem::getPurchaseOrderId, purchaseOrderId);
            return orderItemMapper.selectList(wrapper);
        }, threadPoolTaskExecutor);
    }

    /**
     * 计算商品总数量
     */
    private Integer calculateTotalQuantity(List<TradeProductItem> productItems) {
        if (CollectionUtils.isEmpty(productItems)) {
            return 0;
        }
        return productItems.stream().map(item -> item.getQuantity() != null ? item.getQuantity() : BigDecimal.ZERO)
          .reduce(BigDecimal.ZERO, BigDecimal::add).intValue();
    }

    /**
     * 订单数据容器 - 解决 lambda 表达式变量作用域问题
     */
    private static class OrderDataHolder {

        TzOrderPurchase purchaseOrder;
        List<TzOrderSupplier> supplierOrders;
        List<TzOrderItem> orderItems;

        OrderDataHolder(OrderDataIntegrityResult integrityResult) {
            this.purchaseOrder = integrityResult.existingPurchaseOrder();
            this.supplierOrders = new ArrayList<>(integrityResult.existingSupplierOrders());
            this.orderItems = new ArrayList<>(integrityResult.existingOrderItems());
        }
    }

}
