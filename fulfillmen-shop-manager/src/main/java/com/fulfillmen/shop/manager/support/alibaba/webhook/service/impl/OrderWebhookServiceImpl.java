/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl;

import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemLogisticsStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.support.alibaba.IOrderManager;
import com.fulfillmen.shop.manager.support.alibaba.IPayManager;
import com.fulfillmen.shop.manager.support.alibaba.webhook.convert.AlibabaOrderConvert;
import com.fulfillmen.shop.manager.support.alibaba.webhook.event.OrderWebhookEvent;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderDataSyncService;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderWebhookService;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.support.alibaba.api.request.order.OrderDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.AlipayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.PayWayQueryRequestRecord;
import com.fulfillmen.support.alibaba.api.response.model.PayTypeInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo.NewStepOrder;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeNativeLogisticsInfo.LogisticsItem;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeProductItem;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse.OrderDetail;
import com.fulfillmen.support.alibaba.api.response.pay.AlipayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.PayWayQueryResponse;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderStatusEnums;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailsReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

/**
 * 订单Webhook业务处理服务实现
 *
 * <pre>
 * 实现要点：
 * 1. 统一的异常处理和日志记录
 * 2. 数据完整性检查和补齐
 * 3. 新旧版本数据兼容处理
 * 4. 事件驱动的异步处理
 * 5. 事务管理和数据一致性保证
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/25 16:30
 * @description 订单webhook业务处理服务实现
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderWebhookServiceImpl implements OrderWebhookService {

    private final OrderDataSyncService orderDataSyncService;
    private final IOrderManager orderManager;
    private final IPayManager payManager;
    private final IWmsManager wmsManager;
    private final TzOrderSupplierMapper tzOrderSupplierMapper;
    private final TzOrderPurchaseMapper tzOrderPurchaseMapper;
    private final TzOrderItemMapper tzOrderItemMapper;
    private final ApplicationEventPublisher eventPublisher;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final TransactionTemplate transactionTemplate;
    private final AlibabaOrderConvert alibabaOrderConvert;

    @Override
    public void processOrderWebhook(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        String msgId = messageEvent.getMsgId();

        try {
            log.info("开始处理订单webhook消息: orderId={}, msgId={}, messageType={}, currentStatus={} ",
              orderId, msgId, messageType.getMessageType(), orderMessage.getCurrentStatus());
            // 校验订单状态 是否符合。
            OrderStatusEnums orderStatusEnums = OrderStatusEnums.fromCode(orderMessage.getCurrentStatus());
            if (orderStatusEnums == null) {
                log.warn("暂不支持的处理流程里 : [{}] ", orderMessage.getCurrentStatus());
                return;
            }

            // 1. 异步获取订单数据
            CompletableFuture<OrderDetailResponse.OrderDetail> orderDetailFuture = getOrderDetailAsync(orderMessage.getOrderId());
            CompletableFuture<List<WmsPurchaseOrderDetailsRes>> wmsOrderDetailsFuture = getWmsOrderDetailsAsync(
              orderId);

            // 等待全部任务完成
            CompletableFuture.allOf(orderDetailFuture, wmsOrderDetailsFuture).join();

            // 2. 等待数据获取完成
            OrderDetailResponse.OrderDetail orderDetail = orderDetailFuture.join();
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails = wmsOrderDetailsFuture.join();

            log.info("订单数据获取完成: orderId={}, alibabaOrderDetail={} , alibabaOrderCurrentStatus={}, wmsOrderDetails={} , wmsOrderCurrentStatus={}", orderId, orderDetail,
              wmsOrderDetails);

            // 3. 验证数据有效性
            validateOrderData(orderDetail, wmsOrderDetails, orderId);

            // 4. 检查数据完整性
            OrderDataSyncService.OrderDataIntegrityResult integrityResult = orderDataSyncService.checkOrderDataIntegrity(orderDetail);

            // 5. 如果 wms 为 null , 并且 供应商订单存在，则通过供应商订单获取 wms 采购订单号
            // 这步骤属于补救措施，再次确认数据是否正确
            if (Objects.isNull(wmsOrderDetails) && integrityResult.hasSupplierOrders()) {
                TzOrderSupplier currentOrderSupplier = integrityResult.existingSupplierOrders().stream()
                  .filter(orderSupplier -> Objects.equals(orderSupplier.getPlatformOrderId(), orderId))
                  .findFirst().orElse(null);
                // 通过供应商订单获取 wms 采购订单号
                wmsOrderDetails = getWmsOrderDetailsBySupplierOrderAsync(currentOrderSupplier).join();
                if (Objects.nonNull(wmsOrderDetails)) {
                    log.info("通过供应商订单获取 wms 采购订单成功 : [{}] ", wmsOrderDetails);
                    wmsOrderDetails.getFirst().setOrderId(Long.valueOf(orderId));
                }
            }

            log.info("订单数据完整性检查结果: orderId={}, isComplete={}, isNewVersion={}, missing={}",
              orderId, integrityResult.isDataComplete(), integrityResult.isNewVersionData(),
              integrityResult.getMissingDataDescription());

            // 6. 同步和补齐数据
            OrderContextRecord orderContextRecord = orderDataSyncService.syncAndCompleteOrderData(
              orderId, integrityResult, orderDetail, wmsOrderDetails);

            // 7. 发布数据同步完成事件
            publishDataSyncCompletedEvent(orderMessage, messageEvent, messageType, orderContextRecord);

            // 8. 路由到具体的业务处理逻辑
            routeToBusinessLogic(orderMessage, messageEvent, messageType, orderContextRecord);

            log.info("订单webhook消息处理完成: orderId={}, msgId={}, messageType={}",
              orderId, msgId, messageType.getMessageType());

        } catch (Exception e) {
            log.error("订单webhook消息处理失败: orderId={}, msgId={}, messageType={}, error={}",
              orderId, msgId, messageType.getMessageType(), e.getMessage(), e);

            // 发布处理失败事件
            publishProcessingFailedEvent(orderMessage, messageEvent, messageType, e);
            throw e;
        }
    }

    /**
     * 根据当前的 webhook order 订单 ID，获取到供应商订单，就能获取到 wms 订单
     * <pre>
     * 补救措施
     * </pre>
     *
     * @param orderSupplier 供应商订单
     * @return List<WmsPurchaseOrderDetailsRes>
     */
    private CompletableFuture<List<WmsPurchaseOrderDetailsRes>> getWmsOrderDetailsBySupplierOrderAsync(TzOrderSupplier orderSupplier) {
        return CompletableFuture.supplyAsync(() -> {
            if (Objects.isNull(orderSupplier) || orderSupplier.getWmsPurchaseOrderNo() == null) {
                log.warn("供应商订单不存在 或 wms 订单号不存在 : [{}] ", orderSupplier);
                return null;
            }
            return this.wmsManager.queryOrderDetail(PurchaseOrderDetailReq.builder().purchaseNo(orderSupplier.getWmsPurchaseOrderNo()).build());
        }, threadPoolTaskExecutor);
    }

    /**
     * 异步获取1688订单详情
     */
    private CompletableFuture<OrderDetailResponse.OrderDetail> getOrderDetailAsync(Long orderId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                OrderDetailRequestRecord request = OrderDetailRequestRecord.builder()
                  .webSite("1688")
                  .orderId(orderId)
                  .build();
                return orderManager.getOrderDetail(request);
            } catch (Exception e) {
                log.error("获取1688订单详情失败: orderId={}", orderId, e);
                throw new RuntimeException("获取1688订单详情失败", e);
            }
        }, threadPoolTaskExecutor);
    }

    /**
     * 异步获取WMS订单详情
     */
    private CompletableFuture<List<WmsPurchaseOrderDetailsRes>> getWmsOrderDetailsAsync(String orderId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                PurchaseOrderDetailReq request = PurchaseOrderDetailReq.builder()
                  .orderId(orderId)
                  .build();
                return wmsManager.queryOrderDetail(request);
            } catch (Exception e) {
                log.error("获取WMS订单详情失败: orderId={}", orderId, e);
                return null;
            }
        }, threadPoolTaskExecutor);
    }

    /**
     * 验证订单数据有效性
     */
    private void validateOrderData(OrderDetailResponse.OrderDetail orderDetail,
      List<WmsPurchaseOrderDetailsRes> wmsOrderDetails,
      String orderId) {
        if (orderDetail == null) {
            throw new IllegalArgumentException("1688订单详情为空: orderId=" + orderId);
        }

        if (CollectionUtils.isEmpty(wmsOrderDetails)) {
            log.warn("WMS订单详情为空，可能是旧版数据: orderId={}", orderId);
            // 注意：这里不抛异常，因为旧版数据可能没有WMS记录，不存在没有采购记录。如果没有那么这个订单有问题。
//            throw new IllegalArgumentException("WMS订单详情为空: orderId=" + orderId);
        }
    }

    /**
     * 路由到具体的业务处理逻辑
     */
    private void routeToBusinessLogic(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderMessageTypeEnums messageType, OrderContextRecord orderContextRecord) {
        log.info("路由到具体的业务处理逻辑: orderId={}, messageType={}", orderMessage.getOrderId(), messageType);

        // 路由消息前置做法
        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = orderContextRecord.getWmsPurchaseOrderDetail();
        List<TzOrderSupplier> orderSuppliers = orderContextRecord.tzOrderSuppliers();
        TzOrderPurchase purchase = orderContextRecord.tzOrderPurchase();
        OrderDetail alibabaOrderDetail = orderContextRecord.alibabaOrderDetail();

        // 数据补齐后，需要判断对应前置条件。 原因是 1688 订单 消息存在乱序
        switch (messageType) {
            // 订单创建
            case ORDER_BUYER_VIEW_BUYER_MAKE -> handleOrderCreation(orderMessage, messageEvent, orderContextRecord);
            // 订单付款
            case ORDER_BUYER_VIEW_ORDER_PAY -> {
                // 验证 1688 订单状态处于，发货或收货状态
                if(!Objects.equals(orderContextRecord.getAlibabaOrderStatus(), OrderStatusEnums.WAIT_SELLER_SEND)||
                  !Objects.equals(orderContextRecord.getAlibabaOrderStatus(), OrderStatusEnums.WAIT_BUYER_RECEIVE)){
                    log.warn("1688 订单状态 当前订单状态: [{}] , 期望状态: [{}] ", alibabaOrderDetail.getBaseInfo().getStatus(), OrderStatusEnums.WAIT_SELLER_SEND.getCode());
                    return;
                }
                handleOrderPayment(orderMessage, messageEvent, orderContextRecord);
            }
            // 批量订单付款
            case ORDER_BATCH_PAY -> handleBatchOrderPayment(orderMessage, messageEvent, orderContextRecord);
            // 订单发货
            // 订单部分发货
            case ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS, ORDER_BUYER_VIEW_PART_PART_SENDGOODS -> handleOrderShipment(orderMessage, messageEvent, orderContextRecord);
//            case ORDER_BUYER_VIEW_PART_PART_SENDGOODS -> handlePartialShipment(orderMessage, messageEvent, orderContextRecord);
            // 订单确认收货
            case ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS -> handleOrderConfirmation(orderMessage, messageEvent, orderContextRecord);
            // 订单交易成功
            case ORDER_BUYER_VIEW_ORDER_SUCCESS -> handleOrderCompletion(orderMessage, messageEvent, orderContextRecord);
            // 订单改价
            case ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY -> handleOrderPriceModification(orderMessage, messageEvent, orderContextRecord);
            // 订单关闭
            case ORDER_BUYER_VIEW_ORDER_BOPS_CLOSE, ORDER_BUYER_VIEW_ORDER_BUYER_CLOSE, ORDER_BUYER_VIEW_ORDER_SELLER_CLOSE -> handlerOrderClose(orderMessage, messageEvent, orderContextRecord);
            // 退款 买家申请退款， 买家申请售后退款
            case ORDER_BUYER_VIEW_ORDER_BUYER_REFUND_IN_SALES, ORDER_BUYER_VIEW_ORDER_REFUND_AFTER_SALES -> handlerOrderRefund(orderMessage, messageEvent, orderContextRecord);
            default -> log.warn("未支持的订单消息类型: messageType={}, orderId={}", messageType, orderMessage.getOrderId());
        }
    }

    /**
     * 处理订单退款事件
     */
    private void handlerOrderRefund(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord) {
        log.info("处理订单退款事件: orderId={}", orderMessage.getOrderId());
        // FIXME: 2025/8/5 处理需要退款的订单逻辑
    }

    /**
     * 处理订单关闭事件
     */
    private void handlerOrderClose(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord) {
        log.info("处理订单关闭事件: orderId={}", orderMessage.getOrderId());
        // 1. 判断回调处理 wms 订单取消
        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = orderContextRecord.getWmsPurchaseOrderDetail();
        // 判断 wms 订单是否已取消
        boolean isWmsCancelled = wmsPurchaseOrderDetail.getStatus() == WmsOrderStatusEnum.CANCELED;

        // 2. 判断 naya 供应商订单是否已取消？
        TzOrderSupplier orderSupplier = orderContextRecord.getTzOrderSupplierByOrderId(orderContextRecord.getAlibabaOrderIdStr());
        // 判断供应商订单是否已取消
        boolean isSupplierCancelled = orderSupplier.getStatus() == TzOrderSupplierStatusEnum.CANCELLED;

        // 3. 判断 naya 采购订单是否满足取消条件？
        TzOrderPurchase purchase = orderContextRecord.tzOrderPurchase();
        // 判断采购单是否已取消
        boolean isPurchaseCancelled = purchase.getOrderStatus() == TzOrderPurchaseStatusEnum.ORDER_CANCELLED;

        // 查看是否所有供应商订单已取消
        boolean isAllCancelled = orderContextRecord.tzOrderSuppliers().stream()
          .allMatch(supplier -> supplier.getStatus() == TzOrderSupplierStatusEnum.CANCELLED);
        // 4. 更新订单状态
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            try {
                // 更新供应商订单状态
                if (!isSupplierCancelled) {
                    orderSupplier.setStatus(TzOrderSupplierStatusEnum.CANCELLED);
                    this.tzOrderSupplierMapper.updateById(orderSupplier);
                    // 更新所有商品状态
                    orderContextRecord.getOrderItemsBySupplierOrderId(orderSupplier.getId())
                      .forEach(item -> {
                          item.setStatus(TzOrderItemStatusEnum.CANCELLED);
                      });
                    this.tzOrderItemMapper.updateBatchById(orderContextRecord.tzOrderItems());
                }
                // 判断采购单是否需要取消, 全部供应商订单已取消 且 采购单也未取消
                if (isAllCancelled && !isPurchaseCancelled) {
                    log.info("naya 采购订单, 所有供应商订单已取消，可以取消 : [{}] ", purchase.getPurchaseOrderNo());
                    purchase.setOrderStatus(TzOrderPurchaseStatusEnum.ORDER_CANCELLED);
                    this.tzOrderPurchaseMapper.updateById(purchase);
                }
            } catch (Exception e) {
                log.error("更新数据库失败 : purchaseNo: [{}] ", purchase.getPurchaseOrderNo(), e);
                transactionStatus.setRollbackOnly();
            }
        });
        try {
            // 判断 wms 订单是否已取消
            if (!isWmsCancelled) {
                // 取消 wms 订单
                this.wmsManager.cancelWmsPurchaseOrder(wmsPurchaseOrderDetail.getPurchaseNo(), wmsPurchaseOrderDetail.getCusCode());
            }
        } catch (WmsApiException e) {
            log.error("更新 wms 订单失败 : [{}] ", wmsPurchaseOrderDetail.getPurchaseNo());
        }
    }

    /**
     * 发布数据同步完成事件
     */
    private void publishDataSyncCompletedEvent(OrderMessage orderMessage,
      MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType,
      OrderContextRecord orderContextRecord) {
        OrderWebhookEvent event = OrderWebhookEvent.createDataSyncCompletedEvent(this, orderMessage, messageEvent,
          messageType, orderContextRecord);
        eventPublisher.publishEvent(event);
    }

    /**
     * 发布处理失败事件
     */
    private void publishProcessingFailedEvent(OrderMessage orderMessage,
      MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType,
      Exception error) {
        // TODO: 实现处理失败事件的发布
        log.error("发布订单处理失败事件: orderId={}, error={}", orderMessage.getOrderId(), error.getMessage());
    }

    @Override
    public void handleOrderCreation(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent, OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理订单创建事件: orderId={}", orderId);

        try {

            WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = orderContextRecord.getWmsPurchaseOrderDetail();
            // 获取 支付连接
            WmsPurchaseOrderDetailReq wmsPurchaseOrderDetailReq = WmsPurchaseOrderDetailReq.builder()
              .purchaseNo(wmsPurchaseOrderDetail.getPurchaseNo())
              .orderId(Long.valueOf(orderId))
              .build();
            // 获取供应商订单
            TzOrderSupplier orderSupplier = orderContextRecord.getTzOrderSupplierByOrderId(orderId);
            TzOrderPurchase purchase = orderContextRecord.tzOrderPurchase();

            // 并行异步获取 支付连接和支付方式
            CompletableFuture<Void> alipayUrlFuture = CompletableFuture.runAsync(() -> {
                AlipayUrlResponse alipayUrl = this.payManager
                  .getAlipayUrl(AlipayUrlRequestRecord.builder()
                    .orderIdList(Collections.singletonList(orderMessage.getOrderId())).build());
                // 设置支付链接
                wmsPurchaseOrderDetailReq.setPayUrl(alipayUrl.getPayUrl());
                // 更新供应商信息
                orderSupplier.setPlatformPayUrl(wmsPurchaseOrderDetailReq.getPayUrl());
            }, threadPoolTaskExecutor);

            // 获取支付方式
            CompletableFuture<Void> payWayQueryFuture = CompletableFuture.runAsync(() -> {
                PayWayQueryResponse response = this.payManager
                  .getPayWayQuery(PayWayQueryRequestRecord.of(orderMessage.getOrderId()));
                // 支付方式名称
                String payTypeDesc = response.getResultList().getChannels().stream().map(PayTypeInfo::getName)
                  .collect(Collectors.joining(","));
                String payType = response.getResultList().getChannels().stream().map(PayTypeInfo::getCode)
                  .map(String::valueOf).collect(Collectors.joining(","));
                // 将支付渠道名称用逗号连接，存储到采购订单平台中
                wmsPurchaseOrderDetailReq.setPlatformPayType(payTypeDesc);
                orderSupplier.setPlatformTradeType(payType);
                orderSupplier.setPlatformTradeTypeDesc(payTypeDesc);
            }, threadPoolTaskExecutor);
            // 等待全部完成
            CompletableFuture.allOf(alipayUrlFuture, payWayQueryFuture).join();
            //
            TradeBaseInfo baseInfo = orderContextRecord.alibabaOrderDetail().getBaseInfo();
            List<NewStepOrder> newStepOrderList = baseInfo.getNewStepOrderList();
            if (!CollectionUtils.isEmpty(newStepOrderList)) {
                NewStepOrder newStepOrder = newStepOrderList.getFirst();
                // 优惠金额
                BigDecimal discountFee = newStepOrder.getDiscountFee();
                orderSupplier.setPayableDiscountAmount(discountFee);
                wmsPurchaseOrderDetailReq.setCouponDiscount(discountFee);
            }
            List<TradeProductItem> alibabaOrderProducts = orderContextRecord.getAlibabaOrderProducts();
            // 更新产品总价
            BigDecimal productFinalTotalAmount = alibabaOrderProducts.stream().map(TradeProductItem::getItemAmount)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 重新计算采购单的应付金额
            orderContextRecord.recalculatePayableInformationToPurchaseOrder();
            // 保存
            transactionTemplate.executeWithoutResult(transactionStatus -> {
                try {
                    // 保存供应商订单信息
                    this.tzOrderSupplierMapper.updateById(orderSupplier);
                    // 更新采购单
                    purchase.setPayableGoodsAmount(productFinalTotalAmount);
                    this.tzOrderPurchaseMapper.updateById(purchase);
                    // 同步处理 订单项
                    orderContextRecord.resyncTzOrderItem(orderId);
                    // 更新订单项
                    this.tzOrderItemMapper.updateBatchById(orderContextRecord.tzOrderItems());
                } catch (Exception ex) {
                    log.error("更新供应商和采购单失败 : [{}] ", ex.getMessage(), ex);
                    transactionStatus.setRollbackOnly();
                }
            });
            // 重新 wms 采购订单详情
            List<WmsPurchaseOrderDetailsReq> wmsPurchaseOrderDetailsRequestList = orderContextRecord.resyncWmsPurchaseOrderDetailsReqList();
            wmsPurchaseOrderDetailReq.setOrderDetails(wmsPurchaseOrderDetailsRequestList);
            wmsPurchaseOrderDetailReq.setProductFinalTotalAmount(productFinalTotalAmount);
            // 更新WMS采购订单
            this.wmsManager.updateWmsPurchaseOrder(wmsPurchaseOrderDetailReq);

            // 发布订单创建完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE);

            log.info("订单创建事件处理完成: orderId={}", orderId);
        } catch (WmsApiException e) {
            log.warn("Wms api 创建订单处理失败");
            throw e;
        } catch (Exception e) {
            log.error("订单创建事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handleOrderPayment(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理订单支付事件: orderId={}", orderId);

        try {
            // 1. 更新订单状态为已支付
            updateOrderStatusToPaid(orderId, orderContextRecord);

            // 2. 触发后续业务流程（如通知WMS、发送邮件等）
            triggerPostPaymentProcesses(orderId, orderContextRecord);

            // 3. 发布支付完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_PAY);

            log.info("订单支付事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("订单支付事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handleOrderShipment(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理订单发货事件: orderId={}", orderId);

        try {
            // 1. 更新订单状态为已发货
            updateOrderStatusToShipped(orderId, orderContextRecord);

            // 2. 更新物流信息
            updateShippingInformation(orderId);

            // 3. 发送发货通知
            sendShipmentNotification(orderId);

            // 4. 发布发货完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS);

            log.info("订单发货事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("订单发货事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handleOrderConfirmation(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理订单确认收货事件: orderId={}", orderId);

        try {
            // 1. 更新订单状态为已确认收货
            updateOrderStatusToConfirmed(orderId, orderContextRecord);

            // 2. 处理收货相关业务逻辑
            processOrderConfirmation(orderId, orderContextRecord);

            // 3. 发布确认收货完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS);

            log.info("订单确认收货事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("订单确认收货事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handleOrderCompletion(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理订单完成事件: orderId={}", orderId);

        try {
            // 1. 更新订单状态为已完成
            updateOrderStatusToCompleted(orderId);

            // 2. 处理订单完成相关业务逻辑
            processOrderCompletion(orderId);

            // 3. 发布订单完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SUCCESS);

            log.info("订单完成事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("订单完成事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handleOrderPriceModification(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理订单改价事件: orderId={}", orderId);

        try {
            // 1. 更新订单价格信息
            updateOrderPriceInformation(orderId, orderContextRecord);

            // 2. 同步价格变更到WMS
            syncPriceChangeToWms(orderId, orderContextRecord);

            // 3. 发布改价完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY);

            log.info("订单改价事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("订单改价事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handleBatchOrderPayment(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理批量订单支付事件: orderId={}", orderId);

        try {
            // 批量支付的处理逻辑与单个支付类似
            handleOrderPayment(orderMessage, messageEvent, orderContextRecord);

            log.info("批量订单支付事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("批量订单支付事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    @Override
    public void handlePartialShipment(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
      OrderContextRecord orderContextRecord) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        log.info("处理部分发货事件: orderId={}", orderId);

        try {
            // 1. 更新部分发货状态
            updatePartialShipmentStatus(orderId);

            // 2. 处理部分发货逻辑
            processPartialShipment(orderId);

            // 3. 发布部分发货完成事件
            publishOrderProcessingCompletedEvent(orderMessage, messageEvent,
              OrderMessageTypeEnums.ORDER_BUYER_VIEW_PART_PART_SENDGOODS);

            log.info("部分发货事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("部分发货事件处理失败: orderId={}", orderId, e);
            throw e;
        }
    }

    /**
     * 发布订单处理完成事件
     */
    private void publishOrderProcessingCompletedEvent(OrderMessage orderMessage,
      MessageEvent<OrderMessage> messageEvent,
      OrderMessageTypeEnums messageType) {
        // TODO: 实现订单处理完成事件的发布
        log.debug("发布订单处理完成事件: orderId={}, messageType={}", orderMessage.getOrderId(), messageType);
    }

    /**
     * 更新订单状态为已支付
     */
    private void updateOrderStatusToPaid(String orderId, OrderContextRecord orderContextRecord) {
        log.info("更新订单状态为已支付: orderId={}", orderId);
        TzOrderPurchase tzOrderPurchase = orderContextRecord.tzOrderPurchase();
        TzOrderSupplier orderSupplier = orderContextRecord.getTzOrderSupplierByOrderId(orderId);
        OrderDetail alibabaOrderDetail = orderContextRecord.alibabaOrderDetail();
        TradeBaseInfo baseInfo = alibabaOrderDetail.getBaseInfo();
        // 1. 更新成待发货
        orderSupplier.setStatus(TzOrderSupplierStatusEnum.PENDING_SHIPMENT);
        orderSupplier.setPaymentDate(baseInfo.getPayTime());
        // ---------------- 2. 重新计算实付信息 ----------------
        // 供应商订单
        orderContextRecord.recalculateActualInformationToSupplierOrder(orderId);
        // 总采购订单
        orderContextRecord.recalculateActualInformationToPurchaseOrder();
        // 订单项
        orderContextRecord.tzOrderItems().forEach(item -> {
            alibabaOrderDetail.getProductItems().stream()
              .filter(productItem -> Objects.equals(productItem.getSubItemIdString(), item.getPlatformItemId()))
              .findFirst().ifPresent(productItem -> {
                  item.setActualPrice(productItem.getPrice());
                  item.setQuantity(productItem.getQuantity());
                  item.setStatus(TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS);
                  item.setLogisticsStatus(TzOrderItemLogisticsStatusEnum.getByCode(productItem.getLogisticsStatus()));
                  item.setActualPaymentAmount(productItem.getItemAmount());
              });
        });
        // 保存
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            try {
                this.tzOrderItemMapper.updateBatchById(orderContextRecord.tzOrderItems());
                this.tzOrderSupplierMapper.updateById(orderSupplier);
                this.tzOrderPurchaseMapper.updateById(tzOrderPurchase);
            } catch (Exception e) {
                log.error("更新数据库失败 : purchaseNo: [{}] ", orderContextRecord.getPurchaseOrderNo(), e);
            }
        });
    }

    /**
     * 支付后处理流程 wms 同步
     */
    private void triggerPostPaymentProcesses(String orderId, OrderContextRecord orderContextRecord) {
        log.info("触发支付后处理流程: orderId={}", orderId);
        // 1. 通知WMS系统
        OrderDetail alibabaOrderDetail = orderContextRecord.alibabaOrderDetail();
        TradeBaseInfo baseInfo = alibabaOrderDetail.getBaseInfo();
        List<NewStepOrder> newStepOrderList = baseInfo.getNewStepOrderList();
        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = orderContextRecord.getWmsPurchaseOrderDetail();
        // 获取
        NewStepOrder newStepOrder = newStepOrderList.getFirst();
        BigDecimal alibabaTotalAmount = baseInfo.getTotalAmount().compareTo(newStepOrder.getPaidFee()) == 0 ? baseInfo.getTotalAmount() : newStepOrder.getPaidFee();
        WmsPurchaseOrderDetailReq wmsPurchaseOrderDetailReq = WmsPurchaseOrderDetailReq.builder()
          .purchaseNo(wmsPurchaseOrderDetail.getPurchaseNo())
          .paymentTime(baseInfo.getPayTime())
          .status(WmsOrderStatusEnum.PURCHASED_PENDING_SHIPMENT)
          .orderId(Long.valueOf(orderId))
          // 付款总额
          .alibabaTotalAmount(alibabaTotalAmount)
          .plusDiscount(wmsPurchaseOrderDetail.getTotal().subtract(alibabaTotalAmount))
          .alibabaFinalAmount(baseInfo.getTotalAmount())
          .couponDiscount(baseInfo.getCouponFee())
          .build();
        // 重新同步商品信息
        List<WmsPurchaseOrderDetailsReq> wmsPurchaseOrderDetailsReqs = orderContextRecord.resyncWmsPurchaseOrderDetailsReqList();
        wmsPurchaseOrderDetailReq.setOrderDetails(wmsPurchaseOrderDetailsReqs);
        // 2. 更新WMS采购订单
        this.wmsManager.updateWmsPurchaseOrder(wmsPurchaseOrderDetailReq);
    }

    /**
     * 更新订单状态为已发货
     */
    private void updateOrderStatusToShipped(String orderId, OrderContextRecord orderContextRecord) {
        log.info("更新订单状态为已发货: orderId={}", orderId);
        OrderDetailResponse.OrderDetail orderDetail = orderContextRecord.alibabaOrderDetail();
        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = orderContextRecord.getWmsPurchaseOrderDetail();
        TzOrderPurchase purchase = orderContextRecord.tzOrderPurchase();
        TzOrderSupplier orderSupplier = orderContextRecord.getTzOrderSupplierByOrderId(orderId);

        // 是否 全部发货
        boolean isAllShipped = orderDetail.getProductItems().stream()
          .allMatch(item -> TzOrderItemLogisticsStatusEnum.SHIPPED
            .equals(TzOrderItemLogisticsStatusEnum.getByCode(item.getLogisticsStatus())));
        // 获取 多个运单号
        List<String> trackingNos = orderDetail.getNativeLogistics().getLogisticsItems().stream()
          .map(LogisticsItem::getLogisticsBillNo).toList();
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();

        // 同步更新属性 wms 仓储里处理入库订单和采购单
        wmsPurchaseOrderDetail.setTrackingNo(trackingNos.getFirst());
        wmsPurchaseOrderDetail.setStatus(WmsOrderStatusEnum.SHIPPED_PENDING_RECEIPT);
        wmsPurchaseOrderDetail.setShippingTime(
          Objects.nonNull(baseInfo.getAllDeliveredTime()) ? baseInfo.getAllDeliveredTime() : LocalDateTime.now());

        // 新增入库单
        this.wmsManager.inboundCreateByPurchase(wmsPurchaseOrderDetail);
        // 2. 更新供应商订单状态
        // 3. 更新订单项状态
        orderSupplier.setStatus(
          alibabaOrderConvert.convertSupplierOrderStatus(OrderStatusEnums.fromCode(baseInfo.getStatus())));
        // 4. 记录发货时间
        orderSupplier.setShippedDate(LocalDateTime.now());
        // 使用逗号分割，保存多个运单号
        orderSupplier.setPlatformTrackingNo(String.join(",", trackingNos));
        // 处理采购单状态
        purchase.setOrderStatus(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED);
        // 处理订单项状态
        orderContextRecord.tzOrderItems().forEach(item -> {
            item.setLogisticsStatus(TzOrderItemLogisticsStatusEnum.SHIPPED);
        });
        // 保存
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            try {
                // 保存供应商订单信息
                this.tzOrderSupplierMapper.updateById(orderSupplier);
                // 保存采购单信息
                this.tzOrderPurchaseMapper.updateById(purchase);
                // 保存订单项信息
                this.tzOrderItemMapper.updateBatchById(orderContextRecord.tzOrderItems());
            } catch (Exception ex) {
                log.error("更新供应商和采购单失败 : [{}] ", ex.getMessage(), ex);
                transactionStatus.setRollbackOnly();
            }
        });
    }

    /**
     * 更新物流信息
     */
    private void updateShippingInformation(String orderId) {
        log.info("更新物流信息: orderId={}", orderId);
        // TODO: 实现物流信息更新逻辑
        // 1. 获取最新的物流信息
        // 2. 更新物流状态
        // 3. 记录物流轨迹
    }

    /**
     * 发送发货通知
     */
    private void sendShipmentNotification(String orderId) {
        log.info("发送发货通知: orderId={}", orderId);
        // TODO: 实现发货通知逻辑
        // 1. 发送邮件通知
        // 2. 发送短信通知（如果需要）
        // 3. 推送站内消息
    }

    /**
     * 更新订单状态为已确认收货
     */
    private void updateOrderStatusToConfirmed(String orderId, OrderContextRecord orderContextRecord) {
        log.info("更新订单状态为已确认收货: orderId={}", orderId);
        // TODO: 实现确认收货状态更新逻辑
        OrderDetail alibabaOrderDetail = orderContextRecord.alibabaOrderDetail();
        TzOrderSupplier orderSupplier = orderContextRecord.getTzOrderSupplierByOrderId(orderId);
        TzOrderPurchase purchase = orderContextRecord.tzOrderPurchase();
    }

    /**
     * 处理订单确认收货业务逻辑
     */
    private void processOrderConfirmation(String orderId, OrderContextRecord orderContextRecord) {
        log.info("处理订单确认收货业务逻辑: orderId={}", orderId);
        // TODO: 实现确认收货处理逻辑
        // 1. 更新WMS入库状态
        // 2. 触发质检流程
        // 3. 更新库存信息
    }

    /**
     * 更新订单状态为已完成
     */
    private void updateOrderStatusToCompleted(String orderId) {
        log.info("更新订单状态为已完成: orderId={}", orderId);
        // TODO: 实现订单完成状态更新逻辑
    }

    /**
     * 处理订单完成业务逻辑
     */
    private void processOrderCompletion(String orderId) {
        log.info("处理订单完成业务逻辑: orderId={}", orderId);
        // TODO: 实现订单完成处理逻辑
        // 1. 结算供应商费用
        // 2. 更新统计数据
        // 3. 发送完成通知
    }

    /**
     * 更新订单价格信息
     */
    private void updateOrderPriceInformation(String orderId, OrderContextRecord orderContextRecord) {
        log.info("更新订单价格信息: orderId={}", orderId);
        // 1. 获取最新价格信息
        TzOrderSupplier supplier = orderContextRecord.getTzOrderSupplierByOrderId(orderId);
        TzOrderPurchase purchase = orderContextRecord.tzOrderPurchase();
        List<TzOrderItem> orderItems = orderContextRecord.getOrderItemsBySupplierOrderId(supplier.getId());
        OrderDetail alibabaOrderDetail = orderContextRecord.alibabaOrderDetail();
        // 重新同步商品信息
        orderContextRecord.resyncTzOrderItem(orderId);
        // 更新供应商订单信息 修改重新计算最终实付价格
        orderContextRecord.recalculateActualInformationToSupplierOrder(orderId);
        // 重新计算采购单的实付金额
        orderContextRecord.recalculateActualInformationToPurchaseOrder();
        // 更新数据库
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            try {
                // 保存供应商订单信息
                this.tzOrderSupplierMapper.updateById(supplier);
                // 保存采购单信息
                this.tzOrderPurchaseMapper.updateById(purchase);
                // 保存订单项信息
                this.tzOrderItemMapper.updateBatchById(orderItems);
            } catch (Exception e) {
                log.error("更新数据库失败 : purchaseNo: [{}] ", purchase.getPurchaseOrderNo(), e);
                transactionStatus.setRollbackOnly();
            }
        });
    }

    /**
     * 订单改价同步变更到WMS
     */
    private void syncPriceChangeToWms(String orderId, OrderContextRecord orderContextRecord) {
        log.info("订单改价变更到WMS: orderId={}", orderId);
        // 1. 通知WMS系统
        OrderDetail alibabaOrderDetail = orderContextRecord.alibabaOrderDetail();
        TradeBaseInfo baseInfo = alibabaOrderDetail.getBaseInfo();
        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetailRes = orderContextRecord.getWmsPurchaseOrderDetail();
        // 获取
        WmsPurchaseOrderDetailReq wmsPurchaseOrderDetailReq = WmsPurchaseOrderDetailReq.builder()
          .purchaseNo(wmsPurchaseOrderDetailRes.getPurchaseNo())
          .paymentTime(baseInfo.getPayTime())
          .orderId(Long.valueOf(orderId))
          .plusDiscount(BigDecimal.ZERO)
          .alibabaFinalAmount(baseInfo.getTotalAmount())
          .finalShoppingFee(baseInfo.getShippingFee())
          .build();
        // 重新同步商品信息
        List<WmsPurchaseOrderDetailsReq> wmsPurchaseOrderDetailsReqs = orderContextRecord.resyncWmsPurchaseOrderDetailsReqList();
        wmsPurchaseOrderDetailReq.setOrderDetails(wmsPurchaseOrderDetailsReqs);
        // 2. 更新WMS采购订单
        this.wmsManager.updateWmsPurchaseOrder(wmsPurchaseOrderDetailReq);
    }

    /**
     * 更新部分发货状态
     */
    private void updatePartialShipmentStatus(String orderId) {
        log.info("更新部分发货状态: orderId={}", orderId);
        // TODO: 实现部分发货状态更新逻辑
    }

    /**
     * 处理部分发货逻辑
     */
    private void processPartialShipment(String orderId) {
        log.info("处理部分发货逻辑: orderId={}", orderId);
        // TODO: 实现部分发货处理逻辑
        // 1. 更新已发货商品状态
        // 2. 计算剩余未发货商品
        // 3. 更新物流信息
    }
}
