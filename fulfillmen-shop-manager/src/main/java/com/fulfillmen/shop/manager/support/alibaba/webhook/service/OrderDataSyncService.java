/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.service;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse.OrderDetail;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单数据同步服务接口
 *
 * <pre>
 * 职责：
 * 1. 检查订单数据的存在性和完整性
 * 2. 补齐缺失的订单数据（TzOrderPurchase、TzOrderSupplier、TzOrderItem）
 * 3. 处理新旧版本数据的兼容性
 * 4. 确保数据一致性和关联关系正确
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/25 16:10
 * @description 订单数据同步和补齐服务
 * @since 1.0.0
 */
public interface OrderDataSyncService {

    /**
     * 检查订单数据完整性
     *
     * <pre>
     * 检查以下数据是否存在：
     * 1. TzOrderPurchase（采购订单）
     * 2. TzOrderSupplier（供应商订单）
     * 3. TzOrderItem（订单项）
     * </pre>
     *
     * @param orderDetail 1688订单ID
     * @return 数据完整性检查结果
     */
    OrderDataIntegrityResult checkOrderDataIntegrity(OrderDetail orderDetail);

    /**
     * 同步并补齐订单数据
     *
     * <pre>
     * 根据数据完整性检查结果，补齐缺失的数据：
     * 1. 如果数据完整，直接返回现有数据
     * 2. 如果数据缺失，从API获取数据并创建实体
     * 3. 建立正确的关联关系
     * 4. 初始化状态和时间戳
     * </pre>
     *
     * @param orderId         1688订单ID
     * @param integrityResult 数据完整性检查结果
     * @param orderDetail     1688订单详情
     * @param wmsOrderDetails WMS订单详情列表
     * @return 完整的订单上下文记录
     */
    OrderContextRecord syncAndCompleteOrderData(String orderId,
        OrderDataIntegrityResult integrityResult,
        OrderDetailResponse.OrderDetail orderDetail,
        List<WmsPurchaseOrderDetailsRes> wmsOrderDetails);

    /**
     * 创建采购订单数据
     *
     * @param orderDetail    1688订单详情
     * @param wmsOrderDetail WMS订单详情
     * @return 创建的采购订单
     */
    TzOrderPurchase createPurchaseOrderData(
        OrderDetailResponse.OrderDetail orderDetail,
        WmsPurchaseOrderDetailsRes wmsOrderDetail);

    /**
     * 创建供应商订单数据
     *
     * @param purchaseOrderId 采购订单ID
     * @param orderDetail     1688订单详情
     * @param wmsOrderDetail  WMS订单详情
     * @return 创建的供应商订单列表
     */
    List<TzOrderSupplier> createSupplierOrderData(
        Long purchaseOrderId,
        OrderDetailResponse.OrderDetail orderDetail,
        WmsPurchaseOrderDetailsRes wmsOrderDetail);

    /**
     * 创建订单项数据
     *
     * @param purchaseOrderId 采购订单ID
     * @param supplierOrderId 供应商订单ID
     * @param orderDetail     1688订单详情
     * @param wmsOrderDetail  WMS订单详情
     * @return 创建的订单项列表
     */
    List<TzOrderItem> createOrderItemData(
        Long purchaseOrderId,
        Long supplierOrderId,
        OrderDetailResponse.OrderDetail orderDetail,
        WmsPurchaseOrderDetailsRes wmsOrderDetail);

    /**
     * 订单数据完整性检查结果
     *
     * <AUTHOR>
     * @date 2025/7/25 16:10
     * @description 订单数据完整性检查结果
     * @param hasOrderItems
     * @param hasPurchaseOrder
     * @param hasSupplierOrders
     * @param isDataComplete
     * @param isLegacyData
     * @param existingOrderItems
     * @param existingPurchaseOrder
     * @param existingSupplierOrders
     * @since 1.0.0
     */
    record OrderDataIntegrityResult(
        boolean hasPurchaseOrder,
        boolean hasSupplierOrders,
        boolean hasOrderItems,
        boolean isDataComplete,
        boolean isLegacyData,
        TzOrderPurchase existingPurchaseOrder,
        List<TzOrderSupplier> existingSupplierOrders,
        List<TzOrderItem> existingOrderItems
    ) {

        /**
         * 判断是否为新版数据（三个表都有数据）
         */
        public boolean isNewVersionData() {
            return hasPurchaseOrder && hasSupplierOrders && hasOrderItems;
        }

        /**
         * 判断是否需要数据补齐
         */
        public boolean needsDataSync() {
            return !isDataComplete;
        }

        /**
         * 获取缺失的数据类型描述
         */
        public String getMissingDataDescription() {
            List<String> missing = new ArrayList<>();
            if (!hasPurchaseOrder) {
                missing.add("采购订单");
            }
            if (!hasSupplierOrders) {
                missing.add("供应商订单");
            }
            if (!hasOrderItems) {
                missing.add("订单项");
            }
            return missing.isEmpty() ? "无" : String.join(", ", missing);
        }
    }
}
