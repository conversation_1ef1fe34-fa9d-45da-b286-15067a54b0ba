/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.handler;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemLogisticsStatusEnum;
import com.fulfillmen.starter.core.exception.BusinessException;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo.NewStepOrder;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeProductItem;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderStatusEnums;
import com.fulfillmen.support.alibaba.enums.StepOrderPayStatusEnums;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailsReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * webhook 订单上下文
 *
 * <pre>
 * 将消息组装到一个上下文对象中，方便后续的业务处理
 * 同时，提供一些常用的方法，方便后续的业务处理
 * </pre>
 *
 * @param alibabaOrderDetail         1688 订单详情
 * @param wmsPurchaseOrderDetailsRes wms 采购订单详情
 * @param tzOrderPurchase            采购订单
 * @param tzOrderSuppliers           供应商订单 / 店铺
 * @param tzOrderItems               订单项
 * @param orderMessageTypeEnums      订单消息类型
 * <AUTHOR>
 * @date 2025/7/25 14:57
 * @since 1.0.0
 */
@Slf4j
@Builder
public record OrderContextRecord(
  /*
   * 1688 订单详情
   */
  OrderDetailResponse.OrderDetail alibabaOrderDetail,
  /*
   * wms 采购订单详情
   */
  List<WmsPurchaseOrderDetailsRes> wmsPurchaseOrderDetailsRes,
  /*
   * 采购订单
   */
  TzOrderPurchase tzOrderPurchase,
  /*
   * 供应商订单 / 店铺
   */
  List<TzOrderSupplier> tzOrderSuppliers,
  /*
   * 订单项
   */
  List<TzOrderItem> tzOrderItems,
  /*
   * 订单消息类型
   */
  OrderMessageTypeEnums orderMessageTypeEnums
) {

    /**
     * 获取采购单号
     */
    public String getPurchaseOrderNo() {
        return tzOrderPurchase.getPurchaseOrderNo();
    }

    /**
     * 获取采购单ID
     */
    public Long getPurchaseOrderId() {
        return tzOrderPurchase.getId();
    }

    /**
     * 获取1688 订单号 字符串
     */
    public String getAlibabaOrderIdStr() {
        return alibabaOrderDetail.getBaseInfo().getIdOfStr();
    }

    /**
     * 获取1688 订单号
     *
     * @return Long
     */
    public Long getAlibabaOrderId() {
        return alibabaOrderDetail.getBaseInfo().getId();
    }

    /**
     * 获取 1688 订单枚举状态
     */
    public OrderStatusEnums getAlibabaOrderStatus() {
        return OrderStatusEnums.fromCode(alibabaOrderDetail.getBaseInfo().getStatus());
    }

    /**
     * 获取1688 订单创建时间
     */
    public LocalDateTime getAlibabaOrderCreateTime() {
        return alibabaOrderDetail.getBaseInfo().getCreateTime();
    }

    /**
     * 获取1688 订单商品列表
     */
    public List<TradeProductItem> getAlibabaOrderProducts() {
        return alibabaOrderDetail.getProductItems();
    }

    /**
     * 获取 wms 采购订单详情
     *
     * <pre>
     * note: 如果存在多个 wms 采购订单详情，则取第一个
     * 如果需要获取全部，则直接使用 wmsPurchaseOrderDetailsRes
     * </pre>
     */
    @NonNull
    public WmsPurchaseOrderDetailsRes getWmsPurchaseOrderDetail() {
        return wmsPurchaseOrderDetailsRes.getFirst();
    }

    /**
     * 根据供应商订单ID获取订单项
     */
    public List<TzOrderItem> getOrderItemsBySupplierOrderId(Long supplierOrderId) {
        return this.tzOrderItems.stream().filter(item -> Objects.equals(item.getSupplierOrderId(), supplierOrderId))
          .collect(Collectors.toList());
    }

    /**
     * 重新同步商品信息
     *
     * <pre>
     * 1. 重新同步商品信息
     * 2. 重新计算商品价格
     * 3. 重新计算商品数量
     * 4. 重新计算商品实付金额
     * 5. 重新计算商品实付数量
     * </pre>
     */
    public void resyncTzOrderItem(String orderId) {
        // 重新同步商品信息
        tzOrderItems.stream().filter(orderItem -> Objects.equals(orderItem.getPlatformOrderId(), orderId))
          .forEach(tzOrderItem -> {
              log.info("重新同步商品信息: {}", tzOrderItem);
              // 获取 1688 订单详情信息
              TradeProductItem tradeProductItem = alibabaOrderDetail.getProductItems().stream()
                .filter(productItem -> {
                    // 比较 itemId
                    if (Objects.equals(productItem.getSubItemIdString(), tzOrderItem.getPlatformItemId())) {
                        return true;
                    }
                    // 1. 先比较 ProductId(offerId) 是否相等
                    if (!Objects.equals(productItem.getProductId(),
                      Long.valueOf(tzOrderItem.getPlatformProductId()))) {
                        return false;
                    }
                    // 2. 其次判断是否有 itemId。
                    if (Objects.nonNull(tzOrderItem.getPlatformItemId())) {
                        return Objects.equals(productItem.getSubItemIdString(),
                          tzOrderItem.getPlatformItemId());
                        // 2.1 如果不存在 itemId, 判断 skuId 是否存在
                    } else if (Objects.nonNull(productItem.getSkuId())) {
                        return Objects.equals(productItem.getSkuId().toString(),
                          tzOrderItem.getPlatformSkuId());
                        // 2.2 如果 skuId 不存在，判断 specId 是否存在
                    } else if (Objects.nonNull(productItem.getSpecId())) {
                        return Objects.equals(productItem.getSpecId(), tzOrderItem.getPlatformSpecId());
                    }
                    return false;
                }).findFirst().orElse(null);
              // 1688 订单详情找不到对应的商品详情信息
              if (tradeProductItem == null) {
                  log.warn("未获取到商品详情信息, 1688 订单号: {}, 商品ID: {}, 商品SKU: {}, 商品Spec: {} ", orderId,
                    tzOrderItem.getPlatformProductId(), tzOrderItem.getPlatformSkuId(),
                    tzOrderItem.getPlatformSpecId());
                  return;
              }
              // 更新商品信息
              if (tzOrderItem.getPlatformMetadata() == null) {
                  tzOrderItem.setPlatformMetadata(JacksonUtil.toJsonString(tradeProductItem));
              }
              // 设置价格
              if (tzOrderItem.getPlatformItemId() == null) {
                  tzOrderItem.setPlatformItemId(tradeProductItem.getSubItemIdString());
                  tzOrderItem.setExternalItemId(tradeProductItem.getSubItemIdString());
              }
              // 重新同步计算单价
              tzOrderItem.setPrice(tradeProductItem.getPrice());
              // 实付单价
              tzOrderItem.setActualPrice(tradeProductItem.getItemAmount().divide(tradeProductItem.getQuantity(),
                2, RoundingMode.HALF_UP));
              tzOrderItem.setQuantity(tradeProductItem.getQuantity());
              tzOrderItem.setActualPaymentAmount(tradeProductItem.getItemAmount());
              // 更新物流信息
              TzOrderItemLogisticsStatusEnum itemLogisticsStatusEnum = TzOrderItemLogisticsStatusEnum
                .getByCode(tradeProductItem.getLogisticsStatus());
              tzOrderItem.setLogisticsStatus(itemLogisticsStatusEnum);
              tzOrderItem.setCompletedDatetime(tradeProductItem.getGmtCompleted());
              // TODO：后续补充
          });
    }

    /**
     * PlatformItemId 对应 1688 SubItemId, 根据供应商订单ID 获取订单项,并以 PlatformItemId 字段进行作为 key ，转成 map value 为订单项
     *
     * <pre>
     * 注意：此方法已经自动调用了 {@link #resyncTzOrderItem(String) 重新同步商品信息} 。
     * </pre>
     *
     * @param supplierOrderId 供应商订单ID
     * @return Map
     */
    public Map<String, TzOrderItem> getOrderItemsMapBySupplierOrderId(Long supplierOrderId) {
        resyncTzOrderItem(getAlibabaOrderIdStr());
        return this.tzOrderItems.stream().filter(item -> Objects.equals(item.getSupplierOrderId(), supplierOrderId))
          .collect(Collectors.toMap(TzOrderItem::getPlatformItemId, item -> item));
    }

    /**
     * 重新同步 wms 采购订单详情 并返回 List<WmsPurchaseOrderDetailsReq>
     *
     * <pre>
     * 处理 wms WmsPurchaseOrderDetailsReq
     * 1. 重新同步商品信息
     * 2. 重新计算商品价格
     * 3. 重新计算商品数量
     * 4. 重新计算商品实付金额
     * 5. 重新计算商品实付数量
     * </pre>
     */
    public List<WmsPurchaseOrderDetailsReq> resyncWmsPurchaseOrderDetailsReqList() {
        List<WmsPurchaseOrderDetailsReq> wmsPurchaseOrderDetails = Lists.newArrayList();
        // 重新同步商品信息
        getWmsPurchaseOrderDetail().getOrderDetailsItemRes()
          .forEach(wmsPurchaseOrderDetail -> {
              log.info("重新 wms 同步商品信息: {}", wmsPurchaseOrderDetail);
              // 获取 1688 订单详情信息
              TradeProductItem tradeProductItem = alibabaOrderDetail.getProductItems().stream()
                .filter(productItem -> {
                    // 1. 先比较 ProductId(offerId) 是否相等
                    if (!Objects.equals(productItem.getProductId(),
                      Long.valueOf(wmsPurchaseOrderDetail.getProductId()))) {
                        return false;
                    }
                    // 2. 判断 skuId 是否存在
                    if (Objects.nonNull(productItem.getSkuId())) {
                        // NOTE: wms 仓储订单 订单详情存在一个问题，如果 1688 订单不存在 skuId 和 specId 的时候，会将 ProductId 赋予
                        // SkuId 上。这个主要问题是因为，wms 商品必须要有 sku 信息
                        return Objects.equals(productItem.getSkuId().toString(),
                          wmsPurchaseOrderDetail.getSkuId())
                          || Objects.equals(productItem.getProductId().toString(),
                          wmsPurchaseOrderDetail
                            .getSkuId());
                        // 2.2 如果 skuId 不存在，判断 specId 是否存在
                    } else if (Objects.nonNull(productItem.getSpecId())) {
                        return Objects.equals(productItem.getSpecId(),
                          wmsPurchaseOrderDetail.getVariantId());
                    }
                    return false;
                }).findFirst().orElse(null);
              // 1688 订单详情找不到对应的商品详情信息
              if (tradeProductItem == null) {
                  log.warn("未获取到商品详情信息, 商品ID: {}, 商品SKU: {}, 商品Spec: {} ", wmsPurchaseOrderDetail.getProductId(),
                    wmsPurchaseOrderDetail.getSkuId(),
                    wmsPurchaseOrderDetail.getVariantId());
                  return;
              }
              // 设置价格
              // 重新同步计算单价
              WmsPurchaseOrderDetailsReq wmsPurchaseOrderDetailsReq = WmsPurchaseOrderDetailsReq.builder()
                .build();
              wmsPurchaseOrderDetailsReq.setProductId(wmsPurchaseOrderDetail.getProductId());
              wmsPurchaseOrderDetailsReq.setSkuId(wmsPurchaseOrderDetail.getSkuId());
              wmsPurchaseOrderDetailsReq.setVariantId(wmsPurchaseOrderDetail.getVariantId());
              wmsPurchaseOrderDetailsReq.setFinalUnitPrice(tradeProductItem.getItemAmount()
                .divide(tradeProductItem.getQuantity(), 2, RoundingMode.HALF_UP));
              wmsPurchaseOrderDetailsReq.setQuantity(tradeProductItem.getQuantity().intValue());
              wmsPurchaseOrderDetailsReq.setFinalSubTotalAmount(tradeProductItem.getItemAmount());
              wmsPurchaseOrderDetails.add(wmsPurchaseOrderDetailsReq);
          });
        return wmsPurchaseOrderDetails;
    }

    /**
     * 根据 orderId 获取对应的 供应商订单
     *
     * <pre>
     * note: 一个 1688 订单 - wms 订单 - 供应商订单 一一对应。
     * </pre>
     */
    public TzOrderSupplier getTzOrderSupplierByOrderId(String orderId) {
        return tzOrderSuppliers.stream().filter(tzOrderSupplier -> {
            // 根据 1688 订单号 或者 wms 订单号 匹配
            return Objects.equals(tzOrderSupplier.getPlatformOrderId(), orderId) || Objects
              .equals(tzOrderSupplier.getWmsPurchaseOrderNo(), getWmsPurchaseOrderDetail().getPurchaseNo());
        }).findFirst().orElseThrow(() -> {
            log.error("❌ 未找到对应的供应商订单: orderId={}", orderId);
            return new BusinessException(String.format("未找到对应的供应商订单,%s", orderId));
        });
    }

    /**
     * 重新计算采购订单的应付信息
     */
    public void recalculatePayableInformationToPurchaseOrder() {
        if (CollectionUtils.isEmpty(tzOrderSuppliers)) {
            return;
        }
        // --------------------- 应付信息 ---------------------
        BigDecimal payableDiscountAmount = tzOrderSuppliers.stream().map(TzOrderSupplier::getPayableDiscountAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal payableFreightAmount = tzOrderSuppliers.stream().map(TzOrderSupplier::getPayableFreightAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal payableGoodsAmount = tzOrderSuppliers.stream().map(TzOrderSupplier::getPayableGoodsAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal payableCouponAmount = tzOrderSuppliers.stream().map(TzOrderSupplier::getPayableCouponAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal payablePlusDiscountAmount = tzOrderSuppliers.stream()
          .map(TzOrderSupplier::getPayablePlusDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal payableAmountTotal = tzOrderSuppliers.stream().map(TzOrderSupplier::getPayableAmountTotal)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
        tzOrderPurchase.setPayableDiscountAmount(payableDiscountAmount);
        tzOrderPurchase.setPayableFreightTotal(payableFreightAmount);
        tzOrderPurchase.setPayableGoodsAmount(payableGoodsAmount);
        tzOrderPurchase.setPayableCouponAmount(payableCouponAmount);
        tzOrderPurchase.setPayablePlusDiscountAmount(payablePlusDiscountAmount);
        tzOrderPurchase.setPayableAmountTotal(payableAmountTotal);
    }

    /**
     * 重新计算采购订单的实付信息
     */
    public void recalculateActualInformationToPurchaseOrder() {
        if (CollectionUtils.isEmpty(tzOrderSuppliers)) {
            return;
        }
        // --------------------- 实付信息 ---------------------
        BigDecimal actualDiscountAmount = tzOrderSuppliers.stream().map(TzOrderSupplier::getActualPaymentDiscountAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal actualFreightAmount = tzOrderSuppliers.stream().map(TzOrderSupplier::getActualPaymentFreightAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal actualGoodsAmount = tzOrderSuppliers.stream().map(TzOrderSupplier::getActualPaymentGoodsAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal actualCouponAmount = tzOrderSuppliers.stream().map(TzOrderSupplier::getActualPaymentCouponAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal actualPlusDiscountAmount = tzOrderSuppliers.stream().map(TzOrderSupplier::getActualPaymentPlusAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal actualAmountTotal = tzOrderSuppliers.stream().map(TzOrderSupplier::getActualPaymentAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
        tzOrderPurchase.setActualPaymentDiscountAmount(actualDiscountAmount);
        tzOrderPurchase.setActualPaymentFreightAmount(actualFreightAmount);
        tzOrderPurchase.setActualPaymentGoodsAmount(actualGoodsAmount);
        tzOrderPurchase.setActualPaymentCouponAmount(actualCouponAmount);
        tzOrderPurchase.setActualPaymentPlusAmount(actualPlusDiscountAmount);
        tzOrderPurchase.setActualPaymentAmount(actualAmountTotal);
    }

    /**
     * 重新计算供应商订单的实付信息
     */
    public void recalculateActualInformationToSupplierOrder(String orderId) {
        if (CollectionUtils.isEmpty(tzOrderSuppliers)) {
            return;
        }
        TzOrderSupplier orderSupplier = getTzOrderSupplierByOrderId(orderId);
        // --------------------- 实付信息 ---------------------
        TradeBaseInfo baseInfo = alibabaOrderDetail.getBaseInfo();
        List<NewStepOrder> newStepOrderList = alibabaOrderDetail.getBaseInfo().getNewStepOrderList();
        if (CollectionUtils.isEmpty(newStepOrderList)) {
            return;
        }
        // 重新计算实付信息
        newStepOrderList.forEach(newStepOrder -> {
            // 已付款
            if (newStepOrder.getLastStep()) {
                StepOrderPayStatusEnums payStatusEnums = StepOrderPayStatusEnums.fromCode(newStepOrder.getPayStatus());
                if (payStatusEnums != null && payStatusEnums.equals(StepOrderPayStatusEnums.PAID)) {
                    orderSupplier.setActualPaymentAmount(newStepOrder.getPaidFee());
                    orderSupplier.setActualPaymentFreightAmount(newStepOrder.getPaidPostFee());
                    orderSupplier.setActualPaymentGoodsAmount(baseInfo.getSumProductPayment());
                    orderSupplier.setActualPaymentDiscountAmount(baseInfo.getDiscount().multiply(BigDecimal.valueOf(100)));
                    orderSupplier.setActualPaymentCouponAmount(baseInfo.getCouponFee());
                    orderSupplier.setActualPaymentPlusAmount(orderSupplier.getPayableAmountTotal().subtract(orderSupplier.getActualPaymentAmount()));
                }
            } else {
                log.warn("暂不支持处理多阶段支付 : [{}] ", JacksonUtil.toJsonString(newStepOrderList));
            }

        });
    }

}
