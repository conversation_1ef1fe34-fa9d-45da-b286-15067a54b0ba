/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.context;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Getter;

/**
 * 采购订单上下文
 *
 * <pre>
 * 采购订单上下文用于传递采购订单信息，如采购订单、店铺/供应商订单、订单商品、购物车ID等。
 * TODO: 提供后续状态机设计流转使用
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Builder
@Getter
public class OrderContextDTO {

    /**
     * 采购订单 (主订单)
     */
    private final TzOrderPurchase purchaseOrder;
    /**
     * 店铺/供应商订单 (子订单)
     */
    private final List<TzOrderSupplier> supplierOrders;
    /**
     * 订单商品 (订单项)
     */
    private final List<TzOrderItem> orderItems;

    /**
     * 购物车ID
     *
     * <pre>
     * 如果订单来自购物车，则需要传入购物车ID
     * 如果订单来自商品详情页，则不需要传入购物车ID
     * </pre>
     */
    private final List<Long> shoppingCartIds;

    /**
     * 是否已支付采购单
     *
     * @return true - 已支付 false - 未支付
     */
    public Boolean isPurchaseOrderPayCompleted() {
        return purchaseOrder.getOrderStatus().equals(TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED);
    }

    /**
     * 采购订单是否已取消
     *
     * @return true - 已取消 false - 未取消
     */
    public Boolean isPurchaseOrderCancelled() {
        return purchaseOrder.getOrderStatus().equals(TzOrderPurchaseStatusEnum.ORDER_CANCELLED);
    }

    /**
     * 采购订单是否待支付
     *
     * @return true - 待支付 false - 已支付
     */
    public boolean isPurchaseOrderPendingPayment() {
        return purchaseOrder.getOrderStatus().equals(TzOrderPurchaseStatusEnum.PAYMENT_PENDING);
    }

    /**
     * 判断是否当前归属用户
     * <pre>
     * 如果当前用户ID与采购订单用户ID一致，则为当前用户。
     * 注意：UserContextHolder 中的用户ID为当前登录用户ID，可能与采购订单用户ID不一致。
     * 部分业务，判断需要处理
     * </pre>
     */
    public boolean isCurrentUser(Long userId) {
        return purchaseOrder.getBuyerId().equals(userId);
    }

    /**
     * 获取采购订单用户ID
     */
    public Long getPurchaseOrderUserId() {
        return purchaseOrder.getBuyerId();
    }

    /**
     * 获取采购订单ID
     */
    public Long getPurchaseOrderId() {
        return purchaseOrder.getId();
    }

    /**
     * 获取采购订单号
     */
    public String getPurchaseOrderNo() {
        return purchaseOrder.getPurchaseOrderNo();
    }

    /**
     * 根据供应商订单ID获取订单商品
     */
    public List<TzOrderItem> getOrderItemsBySupplierOrderId(Long supplierOrderId) {
        return this.orderItems.stream().filter(item -> item.getSupplierOrderId().equals(supplierOrderId)).collect(Collectors.toList());
    }

    /**
     * 获取订单总金额 (客户支付的订单总金额 运费+商品+服务费)
     */
    public BigDecimal getCustomerTotalAmount() {
        return purchaseOrder.getCustomerTotalAmount();
    }

    /**
     * 获取订单商品总金额 (客户支付的商品总金额)
     */
    public BigDecimal getCustomerGoodsAmount() {
        return purchaseOrder.getCustomerGoodsAmount();
    }

    /**
     * 获取订单运费 (客户支付的运费)
     */
    public BigDecimal getCustomerTotalFreight() {
        return purchaseOrder.getCustomerTotalFreight();
    }

    /**
     * 获取订单服务费 (客户支付的服务费)
     */
    public BigDecimal getCustomerTotalServiceFee() {
        return purchaseOrder.getServiceFee();
    }

    /**
     * 采购单 支付的汇率 快照
     */
    public BigDecimal getExchangeRateSnapshot() {
        return purchaseOrder.getExchangeRateSnapshot();
    }

    /**
     * 是否全部供应商订单已取消
     */
    public boolean isAllSupplierOrderCancelled() {
        return supplierOrders.stream().allMatch(supplierOrder -> supplierOrder.getStatus() == TzOrderSupplierStatusEnum.CANCELLED);
    }

    /**
     * 扣费的备注说明：支付的订单总费用、商品总金额、运费、服务费、当前汇率
     */
    public String generatePaymentDeductionNote() {
        return String.format("订单总费用: %s, 商品总金额: %s, 运费: %s, 服务费: %s, 当前美元汇率: %s",
            getCustomerTotalAmount(),
            getCustomerGoodsAmount(),
            getCustomerTotalFreight(),
            getCustomerTotalServiceFee(),
            getExchangeRateSnapshot());
    }
}
